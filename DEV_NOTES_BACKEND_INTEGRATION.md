# Backend-Frontend Integration Fix

## 1. API Service Layer Creation

**Create File**: `src/services/api.ts`

```typescript
// API service for backend communication
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

class ApiService {
  private baseURL: string;

  constructor() {
    this.baseURL = API_BASE_URL;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  // Authentication endpoints
  async login(email: string, password: string) {
    const formData = new FormData();
    formData.append('username', email);
    formData.append('password', password);

    return this.request('/token', {
      method: 'POST',
      body: formData,
      headers: {}, // Remove Content-Type for FormData
    });
  }

  async register(username: string, email: string, password: string) {
    return this.request('/users/', {
      method: 'POST',
      body: JSON.stringify({ username, email, password }),
    });
  }

  // Video processing endpoints
  async uploadVideo(file: File, minDuration = 10, maxDuration = 60) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('min_duration', minDuration.toString());
    formData.append('max_duration', maxDuration.toString());

    return this.request('/upload', {
      method: 'POST',
      body: formData,
      headers: {}, // Remove Content-Type for FormData
    });
  }

  async processUrl(url: string, minDuration = 10, maxDuration = 60) {
    return this.request('/process-url', {
      method: 'POST',
      body: JSON.stringify({
        url,
        min_duration: minDuration,
        max_duration: maxDuration,
      }),
    });
  }

  async validateUrl(url: string) {
    return this.request('/validate-url', {
      method: 'POST',
      body: JSON.stringify({ url }),
    });
  }

  // Video editing endpoints
  async trimVideo(file: File, startTime: number, endTime: number) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('start_time', startTime.toString());
    formData.append('end_time', endTime.toString());

    return this.request('/edit/trim', {
      method: 'POST',
      body: formData,
      headers: {},
    });
  }

  async adjustSpeed(file: File, speedFactor: number) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('speed_factor', speedFactor.toString());

    return this.request('/edit/speed', {
      method: 'POST',
      body: formData,
      headers: {},
    });
  }

  async cropVideo(file: File, x1: number, y1: number, x2: number, y2: number) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('x1', x1.toString());
    formData.append('y1', y1.toString());
    formData.append('x2', x2.toString());
    formData.append('y2', y2.toString());

    return this.request('/edit/crop', {
      method: 'POST',
      body: formData,
      headers: {},
    });
  }

  // AI generation endpoints
  async generateScript(prompt: string, image: string, platform: string, duration: number) {
    return this.request('/generate', {
      method: 'POST',
      body: JSON.stringify({ prompt, image, platform, duration }),
    });
  }

  async generateAudio(scriptText: string, sessionName: string, sceneNumber: number, voiceType: string, platform: string) {
    return this.request('/generate-audio', {
      method: 'POST',
      body: JSON.stringify({
        script_text: scriptText,
        session_name: sessionName,
        scene_number: sceneNumber,
        voice_type: voiceType,
        platform,
      }),
    });
  }

  async generateImage(script: any, sessionName: string, sceneNumber: number, mediaType: string, platform: string) {
    return this.request('/generate-image', {
      method: 'POST',
      body: JSON.stringify({
        script,
        session_name: sessionName,
        scene_number: sceneNumber,
        mediaType,
        platform,
      }),
    });
  }

  async generateVideo(sessionName: string, audioFiles: string[], imageFiles: string[], platform: string) {
    return this.request('/generate-video', {
      method: 'POST',
      body: JSON.stringify({
        session_name: sessionName,
        audio_files: audioFiles,
        image_files: imageFiles,
        platform,
      }),
    });
  }

  // Analytics endpoints
  async getVideos(skip = 0, limit = 20) {
    return this.request(`/videos/?skip=${skip}&limit=${limit}`);
  }

  async deleteVideo(videoId: number) {
    return this.request(`/videos/${videoId}`, {
      method: 'DELETE',
    });
  }

  // Health check
  async healthCheck() {
    return this.request('/');
  }
}

export const apiService = new ApiService();
export default apiService;
```

## 2. Backend Connection Test Component

**Create File**: `src/components/BackendConnectionTest.tsx`

```typescript
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import apiService from '@/services/api';

const BackendConnectionTest = () => {
  const [testResults, setTestResults] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const runBackendTests = async () => {
    setIsLoading(true);
    const results: any = {};

    try {
      // Test 1: Health check
      console.log('Testing backend health...');
      try {
        await apiService.healthCheck();
        results.health = { status: 'success', message: 'Backend is running' };
      } catch (error: any) {
        results.health = { status: 'error', message: error.message };
      }

      // Test 2: URL validation
      console.log('Testing URL validation...');
      try {
        const result = await apiService.validateUrl('https://www.youtube.com/watch?v=dQw4w9WgXcQ');
        results.urlValidation = { status: 'success', message: 'URL validation working', data: result };
      } catch (error: any) {
        results.urlValidation = { status: 'error', message: error.message };
      }

      setTestResults(results);
      
      toast({
        title: "Backend tests completed",
        description: "Check results below",
      });
    } catch (error: any) {
      toast({
        title: "Backend tests failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Backend Connection Test</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <Button onClick={runBackendTests} disabled={isLoading}>
            {isLoading ? 'Testing...' : 'Test Backend Connection'}
          </Button>
          
          {Object.entries(testResults).map(([testName, result]: [string, any]) => (
            <div key={testName} className="flex items-center justify-between p-2 border rounded">
              <span className="capitalize">{testName.replace(/([A-Z])/g, ' $1')}</span>
              <Badge variant={result.status === 'success' ? 'default' : 'destructive'}>
                {result.status}
              </Badge>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default BackendConnectionTest;
```

## 3. Integration Testing Commands

```bash
# 1. Start backend
cd backend
python main.py

# 2. Start frontend (new terminal)
npm run dev

# 3. Test backend endpoints
curl http://localhost:8000/
curl -X POST http://localhost:8000/validate-url \
  -H "Content-Type: application/json" \
  -d '{"url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ"}'

# 4. Test frontend-backend connection
# Visit: http://localhost:8080/auth-debug
# Use the "Test Supabase Connection" button
```

## 4. Environment Variables for Integration

**File**: `.env`
```env
# Backend API URL (critical for frontend-backend communication)
VITE_API_URL=http://localhost:8000
```

**File**: `backend/.env`
```env
# CORS origins (allow frontend to connect)
CORS_ORIGINS=["http://localhost:8080", "http://localhost:3000"]
```

## 5. CORS Configuration Fix

**File**: `backend/main.py` (lines 48-54)
The CORS middleware is already configured to allow all origins for development.
For production, update to specific origins:

```python
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:8080"],  # Update for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```
