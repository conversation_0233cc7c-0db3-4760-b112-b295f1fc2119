# Database Connection & Schema Fix

## 1. Supabase Database Schema Setup

**Location**: Supabase Dashboard → SQL Editor
**Execute these SQL commands**:

```sql
-- 1. Create profiles table with correct schema
CREATE TABLE IF NOT EXISTS profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  username TEXT UNIQUE,
  first_name TEXT,
  last_name TEXT,
  full_name TEXT GENERATED ALWAYS AS (
    CASE 
      WHEN first_name IS NOT NULL AND last_name IS NOT NULL 
      THEN first_name || ' ' || last_name
      WHEN first_name IS NOT NULL 
      THEN first_name
      WHEN last_name IS NOT NULL 
      THEN last_name
      ELSE NULL
    END
  ) STORED,
  email TEXT,
  avatar_url TEXT,
  bio TEXT,
  subscription TEXT DEFAULT 'free',
  credits INTEGER DEFAULT 10,
  role TEXT DEFAULT 'user',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- 3. Create RLS Policies
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- 4. Create function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, username, email, first_name, avatar_url)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'username', split_part(NEW.email, '@', 1)),
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.raw_user_meta_data->>'username'),
    NEW.raw_user_meta_data->>'avatar_url'
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Create trigger for automatic profile creation
CREATE OR REPLACE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 6. Create videos table
CREATE TABLE IF NOT EXISTS videos (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  filename TEXT NOT NULL,
  original_url TEXT,
  duration NUMERIC,
  status TEXT DEFAULT 'processing',
  credits_used INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Create video_clips table
CREATE TABLE IF NOT EXISTS video_clips (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  video_id UUID REFERENCES videos(id) ON DELETE CASCADE,
  title TEXT,
  url TEXT NOT NULL,
  duration NUMERIC,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. Enable RLS for videos and clips
ALTER TABLE videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_clips ENABLE ROW LEVEL SECURITY;

-- 9. Create policies for videos
CREATE POLICY "Users can view own videos" ON videos
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own videos" ON videos
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own videos" ON videos
  FOR UPDATE USING (auth.uid() = user_id);

-- 10. Create policies for video_clips
CREATE POLICY "Users can view own clips" ON video_clips
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM videos 
      WHERE videos.id = video_clips.video_id 
      AND videos.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert own clips" ON video_clips
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM videos 
      WHERE videos.id = video_clips.video_id 
      AND videos.user_id = auth.uid()
    )
  );
```

## 2. Updated AuthContext Code

**File**: `src/context/AuthContext.tsx`
**Key Changes Made**:
- Lines 76-129: Added automatic profile creation
- Lines 133-146: Fixed user data mapping to match database schema
- Lines 504-553: Enhanced OAuth error handling

## 3. Database Connection Test

**File**: `src/pages/SupabaseDebug.tsx`
**Test URL**: http://localhost:8080/supabase-debug

**Test Commands**:
```bash
# Test database connection
npm run dev
# Then visit: http://localhost:8080/supabase-debug
# Click "Run Connection Tests"
```

## 4. Backend Database Configuration

**File**: `backend/.env`
```env
# Use SQLite for local development (already configured)
DATABASE_URL=sqlite:///./quikclips.db

# Or use Supabase for production
# DATABASE_URL=postgresql://postgres.ilwlliquljbgikgybbfm:<EMAIL>:5432/postgres
```

## 5. Verification Steps

1. **Check if tables exist**:
   ```sql
   SELECT table_name FROM information_schema.tables 
   WHERE table_schema = 'public';
   ```

2. **Test profile creation**:
   ```sql
   SELECT * FROM profiles LIMIT 5;
   ```

3. **Test RLS policies**:
   ```sql
   SELECT * FROM pg_policies WHERE tablename IN ('profiles', 'videos', 'video_clips');
   ```
