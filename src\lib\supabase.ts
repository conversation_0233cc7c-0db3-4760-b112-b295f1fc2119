
import { createClient } from '@supabase/supabase-js';
import { ENV, isSupabaseConfigured } from '@/lib/env';

// Initialize Supabase client with better error handling
const createSupabaseClient = () => {
  if (!isSupabaseConfigured()) {
    console.warn('Supabase is not fully configured. Some features may not work correctly.');
    // For development, return a client with placeholder values
    return createClient(
      'https://xrbjiesqiibonwuimqwv.supabase.co', 
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhyYmppZXNxaWlib253dWltcXd2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNTAxNzIsImV4cCI6MjA2NTgyNjE3Mn0.aTlCREy27ZQIv0Mr7mUt9vm3Ctszw41KwLx1YE9vgt0'
    );
  }
  
  return createClient(ENV.SUPABASE_URL, ENV.SUPABASE_ANON_KEY);
};

// Initialize and export Supabase client
export const supabase = createSupabaseClient();

// Export a helper for mock data in development
export const useMockData = (): boolean => {
  return !isSupabaseConfigured() || ENV.IS_DEV;
};
