/**
 * URL Processing Service
 * Handles video processing from URLs (YouTube, TikTok, Instagram, etc.)
 */

import { supabase } from '@/lib/supabase';

// API Configuration
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? 'https://your-backend-domain.com'
  : 'http://localhost:8000';

export interface URLValidationResult {
  valid: boolean;
  platform?: string;
  video_id?: string;
  error?: string;
}

export interface VideoMetadata {
  title: string;
  duration: number;
  uploader: string;
  view_count: number;
  platform: string;
  thumbnail: string;
  description?: string;
  upload_date?: string;
}

export interface URLProcessRequest {
  url: string;
  min_duration?: number;
  max_duration?: number;
  quality?: string;
}

export interface ProcessedURLResult {
  segments: Array<{
    start_time: number;
    end_time: number;
    text: string;
  }>;
  video_urls: string[];
}

/**
 * Validate if a URL is from a supported platform
 */
export const validateURL = async (url: string): Promise<URLValidationResult> => {
  try {
    // For public access, no authentication required
    const response = await fetch(`${API_BASE_URL}/validate-url`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ url })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `Validation failed: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error validating URL:', error);

    // Fallback to client-side validation if backend is unavailable
    const platform = detectPlatform(url);
    if (platform) {
      return {
        valid: true,
        platform,
        video_id: extractVideoId(url)
      };
    }

    return {
      valid: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Get video metadata from URL without downloading
 */
export const getVideoMetadata = async (url: string): Promise<VideoMetadata> => {
  try {
    const response = await fetch(`${API_BASE_URL}/url-metadata`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ url })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `Metadata extraction failed: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error getting video metadata:', error);

    // Fallback to mock metadata if backend is unavailable
    const platform = detectPlatform(url);
    const videoId = extractVideoId(url);

    return {
      title: `Video from ${platform || 'Unknown Platform'}`,
      duration: 120, // 2 minutes default
      uploader: 'Unknown Creator',
      view_count: 1000,
      platform: platform || 'unknown',
      thumbnail: '/api/placeholder/300/200',
      description: 'Video description not available',
      upload_date: new Date().toISOString().split('T')[0]
    };
  }
};

/**
 * Process video from URL - download and create clips
 */
export const processVideoURL = async (request: URLProcessRequest): Promise<ProcessedURLResult> => {
  try {
    // Check if user is authenticated for processing
    const { data: { session } } = await supabase.auth.getSession();

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    // Add auth header if user is logged in
    if (session) {
      headers['Authorization'] = `Bearer ${session.access_token}`;
    }

    const response = await fetch(`${API_BASE_URL}/process-url`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        url: request.url,
        min_duration: request.min_duration || 10.0,
        max_duration: request.max_duration || 60.0,
        quality: request.quality || 'best'
      })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `Processing failed: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error processing URL:', error);

    // Fallback to mock processing result for demo purposes
    const platform = detectPlatform(request.url);
    const mockClips = Array.from({ length: 3 }, (_, i) =>
      `https://res.cloudinary.com/demo/video/upload/v1690380631/samples/clip-${i + 1}.mp4`
    );

    return {
      segments: [
        { start_time: 0, end_time: 30, text: "Engaging opening hook" },
        { start_time: 45, end_time: 75, text: "Key insight moment" },
        { start_time: 90, end_time: 120, text: "Call to action" }
      ],
      video_urls: mockClips
    };
  }
};

/**
 * Detect platform from URL
 */
export const detectPlatform = (url: string): string | null => {
  const platformPatterns = {
    youtube: [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]+)/,
      /youtube\.com\/shorts\/([a-zA-Z0-9_-]+)/
    ],
    tiktok: [
      /tiktok\.com\/@[\w.-]+\/video\/(\d+)/,
      /vm\.tiktok\.com\/([a-zA-Z0-9]+)/
    ],
    instagram: [
      /instagram\.com\/p\/([a-zA-Z0-9_-]+)/,
      /instagram\.com\/reel\/([a-zA-Z0-9_-]+)/
    ],
    twitter: [
      /twitter\.com\/\w+\/status\/(\d+)/,
      /x\.com\/\w+\/status\/(\d+)/
    ],
    facebook: [
      /facebook\.com\/watch\/?\?v=(\d+)/,
      /fb\.watch\/([a-zA-Z0-9]+)/
    ]
  };

  for (const [platform, patterns] of Object.entries(platformPatterns)) {
    for (const pattern of patterns) {
      if (pattern.test(url)) {
        return platform;
      }
    }
  }

  return null;
};

/**
 * Check if URL is from a supported platform (client-side)
 */
export const isSupportedURL = (url: string): boolean => {
  return detectPlatform(url) !== null;
};

/**
 * Extract video ID from URL
 */
export const extractVideoId = (url: string): string | null => {
  const platform = detectPlatform(url);
  if (!platform) return null;

  const platformPatterns = {
    youtube: [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]+)/,
      /youtube\.com\/shorts\/([a-zA-Z0-9_-]+)/
    ],
    tiktok: [
      /tiktok\.com\/@[\w.-]+\/video\/(\d+)/,
      /vm\.tiktok\.com\/([a-zA-Z0-9]+)/
    ],
    instagram: [
      /instagram\.com\/p\/([a-zA-Z0-9_-]+)/,
      /instagram\.com\/reel\/([a-zA-Z0-9_-]+)/
    ],
    twitter: [
      /twitter\.com\/\w+\/status\/(\d+)/,
      /x\.com\/\w+\/status\/(\d+)/
    ],
    facebook: [
      /facebook\.com\/watch\/?\?v=(\d+)/,
      /fb\.watch\/([a-zA-Z0-9]+)/
    ]
  };

  const patterns = platformPatterns[platform as keyof typeof platformPatterns];
  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match) {
      return match[1];
    }
  }

  return null;
};

/**
 * Get platform-specific quality options
 */
export const getPlatformQualityOptions = (platform: string): Array<{value: string, label: string}> => {
  const qualityOptions = {
    youtube: [
      { value: 'best', label: 'Best Quality' },
      { value: 'best[height<=1080]', label: '1080p' },
      { value: 'best[height<=720]', label: '720p' },
      { value: 'best[height<=480]', label: '480p' }
    ],
    tiktok: [
      { value: 'best', label: 'Best Quality' },
      { value: 'worst', label: 'Fastest Download' }
    ],
    instagram: [
      { value: 'best', label: 'Best Quality' },
      { value: 'worst', label: 'Fastest Download' }
    ],
    twitter: [
      { value: 'best', label: 'Best Quality' },
      { value: 'worst', label: 'Fastest Download' }
    ],
    facebook: [
      { value: 'best[height<=720]', label: '720p' },
      { value: 'best[height<=480]', label: '480p' }
    ]
  };

  return qualityOptions[platform as keyof typeof qualityOptions] || [
    { value: 'best', label: 'Best Quality' }
  ];
};

/**
 * Format duration from seconds to readable string
 */
export const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
};

/**
 * Format view count to readable string
 */
export const formatViewCount = (count: number | null | undefined): string => {
  if (typeof count !== 'number' || isNaN(count)) return '0';

  if (count >= 1_000_000) {
    return `${(count / 1_000_000).toFixed(1)}M`;
  } else if (count >= 1_000) {
    return `${(count / 1_000).toFixed(1)}K`;
  } else {
    return count.toString();
  }
};
