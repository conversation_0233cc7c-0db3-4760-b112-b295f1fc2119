# 🎬 Advanced Video Processor for SmartClips

## Overview

The Advanced Video Processor is a comprehensive Python module that enhances videos with:
- **Animated Subtitles** with word-level timing
- **Emotion-based Emojis** using AI sentiment analysis
- **Clipart Overlays** for key nouns (2-second display)
- **Short-form Content Creation** optimized for TikTok, Instagram, and YouTube Shorts

## 🚀 Features

### 1. Subtitle Generation
- **Word-level timing** using Whisper AI
- **Multiple styles**: Modern, TikTok, Elegant
- **Animated effects**: Bounce, fade, zoom
- **Customizable fonts and colors**

### 2. Emoji Integration
- **AI-powered emotion analysis** using DistilRoBERTa
- **Context-aware emoji placement**
- **7 emotion categories**: Joy, Sadness, Anger, Fear, Surprise, Disgust, Love
- **Multiple emoji options** per emotion

### 3. Clipart Overlays
- **Automatic noun extraction** using spaCy NLP
- **Image search integration** (Unsplash API ready)
- **2-second display duration** for each clipart
- **Smart positioning** (top-right corner)
- **Fade in/out effects**

### 4. Short-form Content Creation
- **Platform optimization**: <PERSON><PERSON><PERSON>ok (9:16), Instagram (9:16), YouTube Shorts (9:16)
- **Intelligent segmentation** based on content analysis
- **Platform-specific effects**: Zoom, transitions, filters
- **Automatic aspect ratio conversion**

## 📦 Installation

### Required Dependencies
```bash
pip install nltk spacy textblob opencv-python emoji pysrt whisper-timestamped transformers torch torchvision librosa matplotlib seaborn
```

### Download Language Models
```bash
python -m spacy download en_core_web_sm
python -c "import nltk; nltk.download('punkt'); nltk.download('averaged_perceptron_tagger'); nltk.download('wordnet'); nltk.download('stopwords')"
```

## 🛠️ Usage

### Basic Usage
```python
from advanced_video_processor import AdvancedVideoProcessor

# Initialize processor
processor = AdvancedVideoProcessor(openai_api_key="your-key-here")

# Process video with all features
options = {
    'add_subtitles': True,
    'add_emojis': True,
    'add_clipart': True,
    'create_short_form': True,
    'platforms': ['tiktok', 'instagram'],
    'subtitle_style': 'modern',
    'max_short_clips': 3
}

results = processor.process_video_comprehensive(
    video_path="input.mp4",
    output_dir="./output",
    options=options
)
```

### Quick TikTok Clip
```python
from advanced_video_processor import create_quick_tiktok_clip

create_quick_tiktok_clip(
    video_path="input.mp4",
    output_path="tiktok_ready.mp4"
)
```

### API Endpoint Usage
```bash
curl -X POST "http://localhost:8080/advanced-process" \
  -H "Content-Type: application/json" \
  -d '{
    "video_url": "https://example.com/video.mp4",
    "options": {
      "add_subtitles": true,
      "add_emojis": true,
      "add_clipart": true,
      "create_short_form": true,
      "platforms": ["tiktok", "instagram"],
      "subtitle_style": "tiktok",
      "max_short_clips": 3
    }
  }'
```

## 🎨 Customization Options

### Subtitle Styles
- **Modern**: Clean white text with black outline
- **TikTok**: Bold yellow text with heavy outline and bounce effect
- **Elegant**: Sophisticated navy and white styling

### Platform Settings
```python
platform_settings = {
    'tiktok': {
        'aspect_ratio': (9, 16),
        'max_duration': 60,
        'resolution': (1080, 1920)
    },
    'instagram': {
        'aspect_ratio': (9, 16),
        'max_duration': 90,
        'resolution': (1080, 1920)
    },
    'youtube_shorts': {
        'aspect_ratio': (9, 16),
        'max_duration': 60,
        'resolution': (1080, 1920)
    }
}
```

### Emotion-Emoji Mapping
```python
emotion_emojis = {
    'joy': ['😊', '😄', '🎉', '✨', '💫'],
    'sadness': ['😢', '😔', '💔', '😞'],
    'anger': ['😠', '😡', '🔥', '💢'],
    'fear': ['😨', '😰', '😱', '🙈'],
    'surprise': ['😲', '😮', '🤯', '😯'],
    'disgust': ['🤢', '😷', '🤮', '😖'],
    'love': ['❤️', '💕', '😍', '🥰', '💖']
}
```

## 🔧 Technical Details

### Processing Pipeline
1. **Transcript Extraction**: Whisper AI with word-level timestamps
2. **Emotion Analysis**: DistilRoBERTa model for sentiment classification
3. **Noun Extraction**: spaCy NLP for key term identification
4. **Image Search**: Unsplash API integration for clipart
5. **Video Composition**: MoviePy for final video assembly

### Performance Considerations
- **GPU Acceleration**: Supports CUDA for faster processing
- **Memory Management**: Automatic cleanup of temporary files
- **Batch Processing**: Optimized for multiple video processing
- **Error Handling**: Comprehensive error recovery and logging

## 📊 Output Structure

```json
{
  "success": true,
  "message": "Video processed successfully",
  "original_video": "input.mp4",
  "processed_videos": {
    "with_subtitles": "https://cloudinary.com/subtitled.mp4",
    "with_clipart": "https://cloudinary.com/enhanced.mp4"
  },
  "short_form_clips": {
    "tiktok": [
      "https://cloudinary.com/tiktok_1.mp4",
      "https://cloudinary.com/tiktok_2.mp4"
    ],
    "instagram": [
      "https://cloudinary.com/insta_1.mp4"
    ]
  },
  "metadata": {
    "transcript": [...],
    "key_nouns": [...],
    "clipart_urls": {...}
  },
  "processing_time": 45.2
}
```

## 🧪 Testing

Run the test suite:
```bash
python backend/test_advanced_processor.py
```

Make sure to:
1. Place a test video file named `test_video.mp4` in the backend directory
2. Set up your OpenAI API key in environment variables
3. Install all required dependencies

## 🔐 Environment Variables

```bash
OPENAI_API_KEY=your-openai-api-key
UNSPLASH_ACCESS_KEY=your-unsplash-key  # Optional
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret
```

## 🚨 Error Handling

The processor includes comprehensive error handling for:
- **Missing dependencies**: Graceful degradation
- **API failures**: Fallback to placeholder content
- **File I/O errors**: Automatic cleanup and recovery
- **Memory issues**: Efficient resource management

## 🤝 Contributing

To extend the processor:
1. Add new subtitle styles in `_create_subtitle_chunks()`
2. Implement new platform optimizations in `_optimize_for_platform()`
3. Add emotion categories in `emotion_emojis` mapping
4. Integrate new image sources in `search_clipart_images()`

## 📄 License

This module is part of the SmartClips project and follows the same licensing terms.
