// Simple test script to verify authentication endpoints
const testAuth = async () => {
  const baseUrl = 'http://localhost:8080';
  
  console.log('🔍 Testing SmartClips Authentication...\n');
  
  // Test 1: Check if frontend is running
  try {
    const response = await fetch(baseUrl);
    console.log('✅ Frontend is accessible at', baseUrl);
  } catch (error) {
    console.log('❌ Frontend not accessible:', error.message);
    return;
  }
  
  // Test 2: Check environment variables page
  try {
    const response = await fetch(`${baseUrl}/env-check`);
    console.log('✅ Environment check page accessible');
  } catch (error) {
    console.log('❌ Environment check page not accessible:', error.message);
  }
  
  // Test 3: Check auth test page
  try {
    const response = await fetch(`${baseUrl}/auth-test`);
    console.log('✅ Auth test page accessible');
  } catch (error) {
    console.log('❌ Auth test page not accessible:', error.message);
  }
  
  // Test 4: Check login page
  try {
    const response = await fetch(`${baseUrl}/login`);
    console.log('✅ Login page accessible');
  } catch (error) {
    console.log('❌ Login page not accessible:', error.message);
  }
  
  console.log('\n📋 Manual Testing Steps:');
  console.log('1. Open http://localhost:8080/env-check to verify environment variables');
  console.log('2. Open http://localhost:8080/auth-test to test authentication');
  console.log('3. Open http://localhost:8080/login to test Google OAuth login');
  console.log('4. Click "Sign in with Google" button to test OAuth flow');
  
  console.log('\n🔧 Configuration Requirements:');
  console.log('- Google OAuth Client ID: 896094909946-1ksm30dbjg35q7c8a7kvc4g0h2kkq3ct.apps.googleusercontent.com');
  console.log('- Authorized redirect URIs in Google Console should include:');
  console.log('  - http://localhost:8080');
  console.log('  - https://ilwlliquljbgikgybbfm.supabase.co/auth/v1/callback');
  console.log('- Supabase Auth settings should have Google OAuth enabled');
};

// Run the test if this is executed directly
if (typeof window === 'undefined') {
  testAuth().catch(console.error);
}

export default testAuth;
