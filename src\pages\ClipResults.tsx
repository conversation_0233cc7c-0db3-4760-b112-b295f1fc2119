import React, { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  Download, 
  Edit, 
  Play, 
  Clock, 
  TrendingUp, 
  Search,
  Filter,
  Grid,
  List,
  Share2,
  Trash2,
  Star
} from "lucide-react";
import { toast } from "@/hooks/use-toast";
import DashboardLayout from "@/components/Dashboard";
import { useAuth } from "@/context/AuthContext";
import { Navigate } from "react-router-dom";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface VideoClip {
  id: string;
  url: string;
  title: string;
  duration: string;
  vitalityScore: number;
  thumbnail?: string;
  createdAt: string;
  platform: string;
  downloadCount: number;
}

const ClipResults = () => {
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [clips, setClips] = useState<VideoClip[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("vitalityScore");
  const [filterBy, setFilterBy] = useState("all");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [selectedClips, setSelectedClips] = useState<string[]>([]);

  if (!isAuthenticated) {
    return <Navigate to="/login" />;
  }

  useEffect(() => {
    loadClips();
  }, []);

  const loadClips = async () => {
    try {
      setLoading(true);
      
      // Mock data for demonstration
      const mockClips: VideoClip[] = [
        {
          id: "clip-1",
          url: "https://res.cloudinary.com/demo/video/upload/v1690380631/samples/sea-turtle.mp4",
          title: "Engaging Opening Hook",
          duration: "23s",
          vitalityScore: 92,
          thumbnail: "/api/placeholder/300/200",
          createdAt: "2024-01-15T10:30:00Z",
          platform: "youtube",
          downloadCount: 15
        },
        {
          id: "clip-2", 
          url: "https://res.cloudinary.com/demo/video/upload/v1690380631/samples/elephants.mp4",
          title: "Key Insight Moment",
          duration: "31s",
          vitalityScore: 87,
          thumbnail: "/api/placeholder/300/200",
          createdAt: "2024-01-15T10:31:00Z",
          platform: "tiktok",
          downloadCount: 8
        },
        {
          id: "clip-3",
          url: "https://res.cloudinary.com/demo/video/upload/v1690380631/samples/sea-turtle.mp4",
          title: "Emotional Peak",
          duration: "18s",
          vitalityScore: 95,
          thumbnail: "/api/placeholder/300/200",
          createdAt: "2024-01-15T10:32:00Z",
          platform: "instagram",
          downloadCount: 23
        },
        {
          id: "clip-4",
          url: "https://res.cloudinary.com/demo/video/upload/v1690380631/samples/elephants.mp4",
          title: "Call to Action",
          duration: "15s",
          vitalityScore: 78,
          thumbnail: "/api/placeholder/300/200",
          createdAt: "2024-01-15T10:33:00Z",
          platform: "youtube",
          downloadCount: 12
        },
        {
          id: "clip-5",
          url: "https://res.cloudinary.com/demo/video/upload/v1690380631/samples/sea-turtle.mp4",
          title: "Funny Moment",
          duration: "27s",
          vitalityScore: 89,
          thumbnail: "/api/placeholder/300/200",
          createdAt: "2024-01-15T10:34:00Z",
          platform: "tiktok",
          downloadCount: 19
        }
      ];

      setClips(mockClips);
    } catch (error) {
      toast({
        title: "Error loading clips",
        description: "Failed to load your video clips",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const filteredAndSortedClips = clips
    .filter(clip => {
      const matchesSearch = clip.title.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesFilter = filterBy === "all" || clip.platform === filterBy;
      return matchesSearch && matchesFilter;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "vitalityScore":
          return b.vitalityScore - a.vitalityScore;
        case "duration":
          return parseInt(a.duration) - parseInt(b.duration);
        case "downloads":
          return b.downloadCount - a.downloadCount;
        case "recent":
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        default:
          return 0;
      }
    });

  const handleEdit = (clipId: string) => {
    navigate(`/clip-editor/${clipId}`);
  };

  const handleDownload = async (clipId: string) => {
    try {
      const clip = clips.find(c => c.id === clipId);
      if (!clip) return;

      // Mock download functionality
      toast({
        title: "Download started",
        description: `Downloading ${clip.title}...`
      });

      // Update download count
      setClips(prev => prev.map(c => 
        c.id === clipId ? { ...c, downloadCount: c.downloadCount + 1 } : c
      ));
    } catch (error) {
      toast({
        title: "Download failed",
        description: "Failed to download the clip",
        variant: "destructive"
      });
    }
  };

  const handleBatchDownload = async () => {
    if (selectedClips.length === 0) {
      toast({
        title: "No clips selected",
        description: "Please select clips to download",
        variant: "destructive"
      });
      return;
    }

    toast({
      title: "Batch download started",
      description: `Downloading ${selectedClips.length} clips...`
    });
  };

  const getVitalityBadge = (score: number) => {
    if (score >= 90) return { variant: "default" as const, label: "Viral", color: "bg-green-500" };
    if (score >= 80) return { variant: "secondary" as const, label: "High", color: "bg-blue-500" };
    if (score >= 70) return { variant: "outline" as const, label: "Good", color: "bg-yellow-500" };
    return { variant: "outline" as const, label: "Low", color: "bg-gray-500" };
  };

  const getPlatformIcon = (platform: string) => {
    const icons = {
      youtube: "🎥",
      tiktok: "🎵", 
      instagram: "📸",
      twitter: "🐦",
      facebook: "👥"
    };
    return icons[platform as keyof typeof icons] || "📹";
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="container mx-auto py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto py-8 px-4">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold">Your Video Clips</h1>
              <p className="text-muted-foreground">
                {clips.length} clips generated • {selectedClips.length} selected
              </p>
            </div>
            
            <div className="flex gap-2">
              <Button 
                onClick={handleBatchDownload}
                disabled={selectedClips.length === 0}
                variant="outline"
              >
                <Download className="h-4 w-4 mr-2" />
                Download Selected ({selectedClips.length})
              </Button>
              <Button onClick={() => navigate("/smart-clipper")}>
                Create New Clips
              </Button>
            </div>
          </div>

          {/* Filters and Search */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search clips..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger className="w-40">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="vitalityScore">Vitality Score</SelectItem>
                      <SelectItem value="recent">Most Recent</SelectItem>
                      <SelectItem value="duration">Duration</SelectItem>
                      <SelectItem value="downloads">Downloads</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={filterBy} onValueChange={setFilterBy}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Platforms</SelectItem>
                      <SelectItem value="youtube">YouTube</SelectItem>
                      <SelectItem value="tiktok">TikTok</SelectItem>
                      <SelectItem value="instagram">Instagram</SelectItem>
                      <SelectItem value="twitter">Twitter</SelectItem>
                    </SelectContent>
                  </Select>

                  <div className="flex border rounded-md">
                    <Button
                      variant={viewMode === "grid" ? "default" : "ghost"}
                      size="sm"
                      onClick={() => setViewMode("grid")}
                    >
                      <Grid className="h-4 w-4" />
                    </Button>
                    <Button
                      variant={viewMode === "list" ? "default" : "ghost"}
                      size="sm"
                      onClick={() => setViewMode("list")}
                    >
                      <List className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Clips Grid/List */}
          {filteredAndSortedClips.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <Play className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No clips found</h3>
                <p className="text-muted-foreground mb-4">
                  {searchTerm || filterBy !== "all" 
                    ? "Try adjusting your search or filters"
                    : "Create your first video clips to get started"
                  }
                </p>
                <Button onClick={() => navigate("/smart-clipper")}>
                  Create Clips
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className={viewMode === "grid" 
              ? "grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4" 
              : "space-y-4"
            }>
              {filteredAndSortedClips.map((clip) => {
                const vitalityBadge = getVitalityBadge(clip.vitalityScore);
                const isSelected = selectedClips.includes(clip.id);

                return (
                  <Card 
                    key={clip.id} 
                    className={`cursor-pointer transition-all ${
                      isSelected ? "ring-2 ring-primary" : ""
                    } ${viewMode === "list" ? "flex" : ""}`}
                    onClick={() => {
                      setSelectedClips(prev => 
                        prev.includes(clip.id) 
                          ? prev.filter(id => id !== clip.id)
                          : [...prev, clip.id]
                      );
                    }}
                  >
                    <div className={viewMode === "list" ? "flex w-full" : ""}>
                      <div className={viewMode === "list" ? "w-48 flex-shrink-0" : ""}>
                        <div className="relative">
                          <video
                            src={clip.url}
                            className={`w-full rounded-t-lg object-cover ${
                              viewMode === "list" ? "h-32 rounded-l-lg rounded-t-none" : "aspect-video"
                            }`}
                            poster={clip.thumbnail}
                          />
                          <div className="absolute top-2 left-2">
                            <Badge variant="secondary" className="text-xs">
                              {getPlatformIcon(clip.platform)} {clip.platform}
                            </Badge>
                          </div>
                          <div className="absolute top-2 right-2">
                            <Badge {...vitalityBadge} className="text-xs">
                              {clip.vitalityScore}%
                            </Badge>
                          </div>
                          <div className="absolute bottom-2 right-2">
                            <Badge variant="outline" className="text-xs bg-black/50 text-white">
                              {clip.duration}
                            </Badge>
                          </div>
                        </div>
                      </div>

                      <CardContent className={`p-4 ${viewMode === "list" ? "flex-1" : ""}`}>
                        <div className={`space-y-3 ${viewMode === "list" ? "flex flex-col justify-between h-full" : ""}`}>
                          <div>
                            <h3 className="font-medium line-clamp-2">{clip.title}</h3>
                            <div className="flex items-center gap-4 text-sm text-muted-foreground mt-2">
                              <span className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                {clip.duration}
                              </span>
                              <span className="flex items-center gap-1">
                                <TrendingUp className="h-3 w-3" />
                                {clip.vitalityScore}% viral
                              </span>
                              <span className="flex items-center gap-1">
                                <Download className="h-3 w-3" />
                                {clip.downloadCount}
                              </span>
                            </div>
                          </div>

                          <div className="flex gap-2">
                            <Button 
                              variant="outline" 
                              size="sm" 
                              className="flex-1"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDownload(clip.id);
                              }}
                            >
                              <Download className="h-3 w-3 mr-1" />
                              Download
                            </Button>
                            <Button 
                              variant="outline" 
                              size="sm" 
                              className="flex-1"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEdit(clip.id);
                              }}
                            >
                              <Edit className="h-3 w-3 mr-1" />
                              Edit
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </div>
                  </Card>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
};

export default ClipResults;
