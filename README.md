# SmartClips.io

SmartClips.io is an AI-powered video creation and editing platform that helps users create engaging content through smart clipping, avatar generation, and script generation.

## 🚀 Features

- **Smart Clipper**: Automatically extract the most interesting segments from your videos
- **Avatar Creator**: Create engaging avatar videos with AI-generated visuals
- **Script Generator**: Generate compelling scripts and turn them into videos
- **Video Editor**: Edit and enhance your video content
- **Social Integration**: Share your content directly to social media platforms
- **Content Calendar**: Plan and schedule your content releases

## 📋 Tech Stack
### Frontend
- **Framework**: React with TypeScript
- **UI Framework**: Tailwind CSS with Shadcn UI components
- **Build Tool**: Vite
- **Routing**: React Router
- **Form Handling**: React Hook Form with Zod validation
- **State Management**: React Context API
- **Authentication**: Supabase Auth

### Backend
- **Framework**: FastAPI (Python)
- **Authentication**: JWT-based auth + Supabase Auth
- **Database**:
  - SQLAlchemy ORM with SQLite/PostgreSQL
  - Supabase for user management
- **Video Processing**:
  - Cloudinary for storage and delivery
  - FFmpeg for video manipulation
  - MoviePy for video editing
- **AI Integration**:
  - OpenAI API for script generation and image creation
  - ElevenLabs API for voice synthesis
  - Google Cloud Text-to-Speech
- **Deployment**: Docker containerization

## 🔗 Frontend-Backend Integration

The SmartClips application uses a modern client-server architecture with the following integration points:

### API Communication
- **Frontend**: React app running on `http://localhost:8080`
- **Backend**: FastAPI server running on `http://localhost:8000`
- **CORS**: Configured to allow cross-origin requests during development
- **Data Format**: JSON for all API requests and responses

### Key API Endpoints
- `POST /validate-url` - Validate video URLs from supported platforms
- `POST /url-metadata` - Extract video metadata without downloading
- `POST /process-url` - Process videos and generate clips
- `POST /upload` - Upload and process video files
- `POST /token` - Authentication and JWT token generation

### Authentication Flow
1. User authenticates via Supabase (Google OAuth, email/password)
2. Frontend receives JWT token
3. Token included in API requests to backend
4. Backend validates token for protected endpoints

## 🛠️ Local Development Setup

### Prerequisites
- Node.js 18+ and npm
- Python 3.9+
- Git

### Frontend Setup
1. Clone the repository:
   ```bash
   git clone https://github.com/Anish-I/smartclips.git
   cd smartclips
   ```

2. Install frontend dependencies:
   ```bash
   npm install
   ```

3. Create a `.env` file in the root directory:
   ```env
   VITE_SUPABASE_URL=https://xrbjiesqiibonwuimqwv.supabase.co
   VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   VITE_API_URL=http://localhost:8000
   VITE_GOOGLE_CLIENT_ID=493964876159-39vld66dct9n4rqq1karu6s14as56qi5.apps.googleusercontent.com
   ```

4. Start the frontend development server:
   ```bash
   npm run dev
   ```

### Backend Setup
1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Install Python dependencies:
   ```bash
   pip install -r requirements_minimal.txt
   ```

3. Start the backend server:
   ```bash
   python -m uvicorn test_main:app --reload --host 0.0.0.0 --port 8000
   ```

### Verification
- Frontend: http://localhost:8080
- Backend API: http://localhost:8000
- API Health Check: http://localhost:8000/health

## 🏗️ Project Structure

```
smartclips/
├── public/             # Static assets
├── src/
│   ├── components/     # Reusable UI components
│   ├── context/        # React context providers
│   ├── hooks/          # Custom React hooks
│   ├── lib/            # Utility functions and libraries
│   ├── pages/          # Page components
│   ├── App.tsx         # Main application component
│   ├── main.tsx        # Application entry point
│   └── index.css       # Global styles
├── .env                # Environment variables (create this)
├── package.json        # Project dependencies and scripts
└── vite.config.ts      # Vite configuration
```

## 📱 Core Features Explained

### Smart Clipper
**URL Processing Flow:**
1. User enters video URL (YouTube, TikTok, Instagram, etc.)
2. Frontend validates URL using `/validate-url` endpoint
3. System fetches metadata via `/url-metadata` endpoint
4. User configures clipping parameters (duration, quality)
5. Backend processes video via `/process-url` endpoint
6. Generated clips are returned with timestamps and descriptions

**File Upload Flow:**
1. User uploads video file through drag-and-drop interface
2. File is uploaded to Cloudinary or local storage
3. Backend processes video using AI analysis
4. System generates multiple clips based on engagement metrics
5. Clips are made available for download or sharing

### Avatar Creator

Create AI-powered avatar videos with customizable characters and backgrounds. This feature enables:

1. Selecting or uploading avatar templates
2. Adding scripts for the avatar to narrate
3. Customizing voice, appearance, and animations
4. Generating a final video with the avatar presenting your content

### Script Generator

Generate engaging scripts for your videos using AI. Features include:

1. Topic selection and content type specification
2. Tone and style customization
3. Script length and format options
4. Direct integration with video creation tools

## 🔐 Authentication

The application uses Supabase for authentication with the following features:

- Email/password authentication
- Social login (Google, Facebook, Apple)
- User profile management
- Role-based access control

## 🧩 Components

The UI is built using Shadcn UI components, which are based on Radix UI primitives and styled with Tailwind CSS. This provides:

- Accessible and customizable UI components
- Consistent design language
- Dark/light mode support
- Responsive layouts

### Data Flow Architecture

```
Frontend (React) ←→ API Layer ←→ Backend (FastAPI) ←→ AI Services
     ↓                              ↓                    ↓
  UI Components              Database (SQLite)      External APIs
     ↓                              ↓                    ↓
  State Management           User/Video Models    OpenAI/Cloudinary
```

## 🖥️ Backend Architecture

The SmartClips backend is built with FastAPI, providing a robust and high-performance API for video processing and AI integration.

### API Endpoints

- **POST /validate-url**: Validate video URLs from supported platforms
- **POST /url-metadata**: Extract video metadata without downloading
- **POST /process-url**: Process videos and generate clips
- **POST /upload**: Upload and process video files
- **POST /token**: Authentication endpoint for JWT tokens
- **GET /health**: Health check endpoint

### Database Schema

The backend uses SQLAlchemy ORM with the following main models:

- **User**: Stores user information, authentication details, and subscription status
- **Video**: Tracks uploaded videos and their processing status
- **VideoClip**: Stores information about generated video clips

### Video Processing Pipeline

1. **Upload/URL**: Videos are uploaded to Cloudinary or processed from URLs
2. **Validation**: Platform support and URL validation
3. **Metadata**: Extract video information (title, duration, thumbnail)
4. **Processing**: AI analysis to identify engaging segments
5. **Clipping**: Generate clips with timestamps and descriptions
6. **Storage**: Processed clips are stored and made available via URLs

### AI Integration

The backend integrates with multiple AI services:

- **OpenAI**: Used for script generation and creative content
- **ElevenLabs**: Provides high-quality voice synthesis for narration
- **Google Cloud TTS**: Alternative text-to-speech service
- **Cloudinary**: Video storage, processing, and delivery

## 🧪 Testing the Integration

### Manual Testing Steps

1. **Start Backend**:
   ```bash
   cd backend
   python -m uvicorn test_main:app --reload --host 0.0.0.0 --port 8000
   ```

2. **Start Frontend**:
   ```bash
   npm run dev
   ```

3. **Test API Endpoints**:
   ```bash
   # Health check
   curl http://localhost:8000/health

   # URL validation
   curl -X POST http://localhost:8000/validate-url \
     -H "Content-Type: application/json" \
     -d '{"url": "https://www.youtube.com/watch?v=test123"}'
   ```

4. **Test Frontend Integration**:
   - Visit http://localhost:8080
   - Navigate to SmartClipper
   - Enter a YouTube URL
   - Verify API calls in browser dev tools
## ⚠️ Known Issues & Limitations

### Current Status
- **Integration Status**: ✅ Verified and working
- **Authentication**: ✅ Supabase + Google OAuth configured
- **API Communication**: ✅ Frontend-backend communication established
- **Video Processing**: ⚠️ Using mock data for development

### Known Issues
1. **Full Backend Dependencies**: The complete backend requires additional dependencies (spaCy, advanced video processing libraries)
2. **Production URLs**: Update API URLs for production deployment
3. **Error Handling**: Some edge cases in video processing need refinement
4. **File Upload Size**: Large video files may timeout during processing

### Development Notes
- Currently using `test_main.py` for simplified backend testing
- Mock data responses for video processing endpoints
- CORS configured for development (update for production)

## 🚀 Deployment

### Frontend Deployment
```bash
npm run build
```
The build artifacts will be stored in the `dist/` directory.

### Backend Deployment
```bash
# Using Docker
docker build -t smartclips-backend ./backend
docker run -p 8000:8000 smartclips-backend

# Or direct deployment
cd backend
uvicorn main:app --host 0.0.0.0 --port 8000
```

### Environment Variables for Production
```env
# Frontend
VITE_API_URL=https://your-backend-domain.com
VITE_SUPABASE_URL=your_production_supabase_url

# Backend
CORS_ORIGINS=["https://your-frontend-domain.com"]
DATABASE_URL=your_production_database_url
```

## 📄 License

[MIT](LICENSE)

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 Project Origin

This project was originally created with [Lovable](https://lovable.dev/projects/3ba4ae96-5ecb-4089-93a3-66ad5d0c8015).
