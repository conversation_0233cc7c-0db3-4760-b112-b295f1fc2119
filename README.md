# SmartClips.io

SmartClips.io is an AI-powered video creation and editing platform that helps users create engaging content through smart clipping, avatar generation, and script generation.

## 🚀 Features

- **Smart Clipper**: Automatically extract the most interesting segments from your videos
- **Avatar Creator**: Create engaging avatar videos with AI-generated visuals
- **Script Generator**: Generate compelling scripts and turn them into videos
- **Video Editor**: Edit and enhance your video content
- **Social Integration**: Share your content directly to social media platforms
- **Content Calendar**: Plan and schedule your content releases

## 📋 Tech Stack

<<<<<<< HEAD
- **Frontend**: React with TypeScript
- **UI Framework**: Tailwind CSS with Shadcn UI components
- **Authentication**: Supabase Auth
- **Database**: Supabase
- **Build Tool**: Vite
- **Routing**: React Router
- **Form Handling**: React Hook Form with Zod validation
- **Video Processing**: Cloudinary

## 🛠️ Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/smartclips.git
   cd smartclips
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Create a `.env` file in the root directory with the following variables:
   ```
   VITE_SUPABASE_URL=your_supabase_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

4. Start the development server:
   ```bash
   npm run dev
   ```

## 🏗️ Project Structure

```
smartclips/
├── public/             # Static assets
├── src/
│   ├── components/     # Reusable UI components
│   ├── context/        # React context providers
│   ├── hooks/          # Custom React hooks
│   ├── lib/            # Utility functions and libraries
│   ├── pages/          # Page components
│   ├── App.tsx         # Main application component
│   ├── main.tsx        # Application entry point
│   └── index.css       # Global styles
├── .env                # Environment variables (create this)
├── package.json        # Project dependencies and scripts
└── vite.config.ts      # Vite configuration
```

## 📱 Core Features Explained

### Smart Clipper

=======
### Frontend
- **Framework**: React with TypeScript
- **UI Framework**: Tailwind CSS with Shadcn UI components
- **Build Tool**: Vite
- **Routing**: React Router
- **Form Handling**: React Hook Form with Zod validation
- **State Management**: React Context API

### Backend
- **Framework**: FastAPI (Python)
- **Authentication**: JWT-based auth + Supabase Auth
- **Database**:
  - SQLAlchemy ORM with SQLite/PostgreSQL
  - Supabase for user management
- **Video Processing**:
  - Cloudinary for storage and delivery
  - FFmpeg for video manipulation
  - MoviePy for video editing
- **AI Integration**:
  - OpenAI API for script generation and image creation
  - ElevenLabs API for voice synthesis
  - Google Cloud Text-to-Speech
- **Deployment**: Docker containerization

## 🛠️ Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/smartclips.git
   cd smartclips
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Create a `.env` file in the root directory with the following variables:
   ```
   VITE_SUPABASE_URL=your_supabase_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

4. Start the development server:
   ```bash
   npm run dev
   ```

## 🏗️ Project Structure

```
smartclips/
├── public/             # Static assets
├── src/
│   ├── components/     # Reusable UI components
│   ├── context/        # React context providers
│   ├── hooks/          # Custom React hooks
│   ├── lib/            # Utility functions and libraries
│   ├── pages/          # Page components
│   ├── App.tsx         # Main application component
│   ├── main.tsx        # Application entry point
│   └── index.css       # Global styles
├── .env                # Environment variables (create this)
├── package.json        # Project dependencies and scripts
└── vite.config.ts      # Vite configuration
```

## 📱 Core Features Explained

### Smart Clipper

>>>>>>> f0d223c (modified instructions)
The Smart Clipper feature allows users to upload videos and automatically extract the most engaging segments based on AI analysis. Users can:

1. Upload videos to Cloudinary
2. Specify clip parameters (duration, number of clips)
3. Provide a prompt to guide the AI in selecting relevant segments
4. Download or share the generated clips

### Avatar Creator

Create AI-powered avatar videos with customizable characters and backgrounds. This feature enables:

1. Selecting or uploading avatar templates
2. Adding scripts for the avatar to narrate
3. Customizing voice, appearance, and animations
4. Generating a final video with the avatar presenting your content

### Script Generator

Generate engaging scripts for your videos using AI. Features include:

1. Topic selection and content type specification
2. Tone and style customization
3. Script length and format options
4. Direct integration with video creation tools

## 🔐 Authentication

The application uses Supabase for authentication with the following features:

- Email/password authentication
- Social login (Google, Facebook, Apple)
- User profile management
- Role-based access control

## 🧩 Components

The UI is built using Shadcn UI components, which are based on Radix UI primitives and styled with Tailwind CSS. This provides:

- Accessible and customizable UI components
- Consistent design language
- Dark/light mode support
- Responsive layouts

<<<<<<< HEAD
=======
## 🖥️ Backend Architecture

The SmartClips backend is built with FastAPI, providing a robust and high-performance API for video processing and AI integration.

### API Endpoints

- **/upload**: Upload and process videos
- **/generate**: Generate video scripts using AI
- **/generate-audio**: Create audio narration from scripts
- **/generate-image**: Generate images based on script descriptions
- **/generate-video**: Combine audio and images into video segments
- **/token**: Authentication endpoint for JWT tokens
- **/users/**: User management endpoints

### Database Schema

The backend uses SQLAlchemy ORM with the following main models:

- **User**: Stores user information, authentication details, and subscription status
- **Video**: Tracks uploaded videos and their processing status
- **VideoClip**: Stores information about generated video clips

### Video Processing Pipeline

1. **Upload**: Videos are uploaded to Cloudinary or local storage
2. **Transcription**: Audio is extracted and transcribed
3. **Segmentation**: Transcript is analyzed to identify engaging segments
4. **Clipping**: Selected segments are extracted as individual clips
5. **Storage**: Processed clips are stored and made available via URLs

### AI Integration

The backend integrates with multiple AI services:

- **OpenAI**: Used for script generation and creative content
- **ElevenLabs**: Provides high-quality voice synthesis for narration
- **Google Cloud TTS**: Alternative text-to-speech service

### Containerization

The application is containerized using Docker, with services defined in docker-compose.yml:

- **backend**: FastAPI application
- **mongodb**: Database for storing application data

>>>>>>> f0d223c (modified instructions)
## 🚀 Deployment

To build the application for production:

```bash
npm run build
```

The build artifacts will be stored in the `dist/` directory.

## 🧪 Development

For development with hot-reload:

```bash
npm run dev
```

For linting:

```bash
npm run lint
```

## 📄 License

[MIT](LICENSE)

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 Project Origin

This project was originally created with [Lovable](https://lovable.dev/projects/3ba4ae96-5ecb-4089-93a3-66ad5d0c8015).
