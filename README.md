# 🎬 SmartClips - AI-Powered Video Creation Platform

SmartClips is a comprehensive AI-powered video creation and editing platform that transforms long-form content into engaging short-form videos. Built with React + TypeScript frontend and FastAPI backend, it provides intelligent video processing, automated subtitle generation, emoji integration, and platform-optimized content creation.

## ✨ Core Features

### 🎯 Smart Video Processing
- **Intelligent Clipping**: AI-powered extraction of engaging video segments
- **Advanced Subtitles**: Word-level timing with animated effects (Modern, TikTok, Elegant styles)
- **Emotion-Based Emojis**: AI sentiment analysis with context-aware emoji placement
- **Clipart Overlays**: Automatic noun extraction with 2-second image displays
- **Platform Optimization**: TikTok, Instagram, YouTube Shorts ready (9:16 aspect ratio)

### 🤖 AI-Powered Content Creation
- **Avatar Creator**: Generate AI avatar videos with customizable characters
- **Script Generator**: Create compelling scripts using OpenAI GPT models
- **Voice Synthesis**: ElevenLabs and Google Cloud TTS integration
- **Image Generation**: AI-powered scene creation and visual content

### 🔧 Advanced Video Tools
- **FFmpeg Integration**: Professional video processing and manipulation
- **Multi-format Support**: YouTube, TikTok, Instagram, Twitter video processing
- **Batch Processing**: Handle multiple videos simultaneously
- **Cloud Storage**: Cloudinary integration for scalable media management

## 🏗️ Technology Stack

### 🎨 Frontend Architecture
- **Framework**: React 18 + TypeScript + Vite
- **UI Components**: Shadcn UI + Radix UI primitives
- **Styling**: Tailwind CSS with custom design system
- **Routing**: React Router v6 with protected routes
- **Forms**: React Hook Form + Zod validation
- **State Management**: React Context + Custom hooks
- **Authentication**: Supabase Auth + Google OAuth

### ⚙️ Backend Infrastructure
- **API Framework**: FastAPI (Python 3.9+)
- **Authentication**: JWT tokens + Supabase integration
- **Database**: SQLAlchemy ORM (SQLite/PostgreSQL)
- **Video Processing**:
  - FFmpeg for video manipulation
  - MoviePy for Python video editing
  - Whisper AI for transcript generation
  - OpenCV for computer vision tasks

### 🤖 AI & ML Integration
- **Language Models**: OpenAI GPT-4 for content generation
- **Speech Processing**: Whisper for transcription, ElevenLabs for TTS
- **Computer Vision**: Transformers, spaCy NLP, OpenCV
- **Emotion Analysis**: DistilRoBERTa for sentiment classification

### ☁️ Cloud Services
- **Media Storage**: Cloudinary for video/image processing
- **Authentication**: Supabase for user management
- **Deployment**: Docker containerization ready
- **APIs**: RESTful API design with automatic documentation

## 🚀 Quick Start Guide

### Prerequisites
- **Node.js** 18+ and npm
- **Python** 3.9+
- **Git** for version control
- **FFmpeg** for video processing

### 🔧 Installation & Setup

#### 1. Clone Repository
```bash
git clone https://github.com/Anish-I/smartclips.git
cd smartclips
```

#### 2. Frontend Setup
```bash
# Install dependencies
npm install

# Create environment file
cp .env.example .env

# Configure environment variables
VITE_SUPABASE_URL=https://xrbjiesqiibonwuimqwv.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_API_URL=http://localhost:8000
VITE_GOOGLE_CLIENT_ID=************-39vld66dct9n4rqq1karu6s14as56qi5.apps.googleusercontent.com

# Start development server
npm run dev
```

#### 3. Backend Setup
```bash
cd backend

# Install Python dependencies
pip install -r requirements.txt

# Install additional AI dependencies
pip install nltk spacy textblob opencv-python emoji pysrt whisper-timestamped transformers torch

# Download language models
python -m spacy download en_core_web_sm

# Configure environment variables
export OPENAI_API_KEY=your_openai_api_key
export CLOUDINARY_CLOUD_NAME=your_cloudinary_name
export CLOUDINARY_API_KEY=your_cloudinary_key
export CLOUDINARY_API_SECRET=your_cloudinary_secret

# Start backend server
python -m uvicorn test_main:app --reload --host 0.0.0.0 --port 8000
```

### 🌐 Access Points
- **Frontend**: http://localhost:8080
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

## 🎬 Advanced Video Processing Features

### 🎤 Intelligent Subtitle Generation
```python
# Create TikTok-style subtitles with emojis
from advanced_video_processor import create_quick_tiktok_clip

create_quick_tiktok_clip(
    video_path="input.mp4",
    output_path="tiktok_ready.mp4"
)
```

**Features:**
- **Word-level timing** using Whisper AI transcription
- **Multiple styles**: Modern, TikTok, Elegant with custom fonts
- **Animated effects**: Bounce, fade, zoom transitions
- **Emotion-based emojis**: AI sentiment analysis with 7 emotion categories

### 🖼️ Smart Clipart Integration
- **Automatic noun extraction** using spaCy NLP
- **2-second display duration** for key visual elements
- **Smart positioning** with fade in/out effects
- **Image search integration** (Unsplash API ready)

### 📱 Platform-Optimized Content
```python
# Process video for multiple platforms
options = {
    'add_subtitles': True,
    'add_emojis': True,
    'add_clipart': True,
    'create_short_form': True,
    'platforms': ['tiktok', 'instagram', 'youtube_shorts'],
    'subtitle_style': 'tiktok',
    'max_short_clips': 3
}

results = processor.process_video_comprehensive(
    video_path="input.mp4",
    output_dir="./output",
    options=options
)
```

### 🔧 API Integration
```bash
# Advanced video processing endpoint
curl -X POST "http://localhost:8000/advanced-process" \
  -H "Content-Type: application/json" \
  -d '{
    "video_url": "https://example.com/video.mp4",
    "options": {
      "add_subtitles": true,
      "add_emojis": true,
      "add_clipart": true,
      "create_short_form": true,
      "platforms": ["tiktok", "instagram"],
      "subtitle_style": "tiktok"
    }
  }'
```

## 📁 Project Architecture

```
smartclips/
├── 🎨 Frontend (React + TypeScript)
│   ├── src/
│   │   ├── components/         # Reusable UI components
│   │   │   ├── ui/            # Shadcn UI components
│   │   │   ├── FeatureFlow.tsx # Multi-step workflows
│   │   │   └── UnifiedFlow.tsx # Unified user experience
│   │   ├── pages/             # Main application pages
│   │   │   ├── SmartClipper.tsx    # Video clipping interface
│   │   │   ├── AvatarCreator.tsx   # AI avatar generation
│   │   │   ├── VideoGenerator.tsx  # Script-to-video creation
│   │   │   └── ClipEditor.tsx      # Advanced video editing
│   │   ├── services/          # API integration layer
│   │   │   ├── videoProcessingService.ts
│   │   │   ├── avatarService.ts
│   │   │   └── openAiService.ts
│   │   ├── context/           # React context providers
│   │   ├── hooks/             # Custom React hooks
│   │   └── lib/               # Utility functions
│   ├── public/                # Static assets
│   └── package.json           # Frontend dependencies
├── 🐍 Backend (FastAPI + Python)
│   ├── main.py                # Main FastAPI application
│   ├── advanced_video_processor.py  # AI video processing
│   ├── video_processing.py    # Core video operations
│   ├── models.py              # Database models
│   ├── database.py            # Database configuration
│   ├── storage.py             # Cloud storage integration
│   ├── url_processor.py       # URL video processing
│   └── requirements.txt       # Python dependencies
├── 🐳 Deployment
│   ├── docker-compose.yml     # Container orchestration
│   ├── Dockerfile             # Backend containerization
│   └── vercel.json            # Frontend deployment
└── 📚 Documentation
    └── README.md              # This comprehensive guide
```

## 🔧 Core API Endpoints

### 🎬 Video Processing APIs
```bash
# Validate video URL from supported platforms
POST /validate-url
{
  "url": "https://www.youtube.com/watch?v=example"
}

# Extract video metadata without downloading
POST /url-metadata
{
  "url": "https://www.youtube.com/watch?v=example"
}

# Process video and generate clips
POST /process-url
{
  "url": "https://www.youtube.com/watch?v=example",
  "min_duration": 10.0,
  "max_duration": 60.0,
  "quality": "best"
}

# Advanced video processing with AI features
POST /advanced-process
{
  "video_url": "https://example.com/video.mp4",
  "options": {
    "add_subtitles": true,
    "add_emojis": true,
    "add_clipart": true,
    "create_short_form": true,
    "platforms": ["tiktok", "instagram"],
    "subtitle_style": "tiktok"
  }
}

# Upload and process video files
POST /upload
Content-Type: multipart/form-data
```

### 🤖 AI Content Generation APIs
```bash
# Generate video scripts
POST /generate
{
  "prompt": "Create a script about AI technology",
  "platform": "youtube",
  "duration": 60
}

# Generate audio from text
POST /generate-audio
{
  "script_text": "Hello, welcome to SmartClips",
  "voice_type": "professional",
  "language_code": "en-US"
}

# Generate images for scenes
POST /generate-image
{
  "script": {"description": "A futuristic AI workspace"},
  "session_name": "demo_session",
  "scene_number": 1
}
```

### 🔐 Authentication APIs
```bash
# JWT token authentication
POST /token
{
  "username": "<EMAIL>",
  "password": "secure_password"
}

# User registration
POST /users/
{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "secure_password"
}
```

## 🔐 Authentication & Security

### Supabase Integration
- **Google OAuth**: Configured with client ID `************-39vld66dct9n4rqq1karu6s14as56qi5.apps.googleusercontent.com`
- **Email/Password**: Traditional authentication with secure password hashing
- **JWT Tokens**: Stateless authentication for API requests
- **Role-based Access**: Free, Pro, Enterprise subscription tiers

### Security Features
- **CORS Protection**: Configured for development and production environments
- **Input Validation**: Zod schemas for frontend, Pydantic models for backend
- **Rate Limiting**: API endpoint protection against abuse
- **Secure File Upload**: Cloudinary integration with signed uploads

## 🎨 UI/UX Design System

### Shadcn UI Components
Built on **Radix UI primitives** with **Tailwind CSS** styling:
- **Accessible**: WCAG 2.1 compliant components
- **Customizable**: CSS variables for theming
- **Responsive**: Mobile-first design approach
- **Dark/Light Mode**: Automatic theme switching

### Key UI Components
- **FeatureFlow**: Multi-step workflow management
- **UnifiedFlow**: Streamlined user experience
- **ClipEditor**: Advanced video editing interface
- **VideoPlayer**: Custom video playback controls

## 🧪 Testing & Development

### Running Tests
```bash
# Backend testing
cd backend
python test_advanced_processor.py

# Frontend testing
npm run test

# API endpoint testing
curl http://localhost:8000/health
```

### Development Workflow
1. **Start Backend**: `python -m uvicorn test_main:app --reload --host 0.0.0.0 --port 8000`
2. **Start Frontend**: `npm run dev`
3. **Test Integration**: Visit http://localhost:8080
4. **API Documentation**: http://localhost:8000/docs

### Example Usage
```python
# Quick TikTok clip creation
from advanced_video_processor import create_quick_tiktok_clip

create_quick_tiktok_clip(
    video_path="sample.mp4",
    output_path="tiktok_ready.mp4"
)

# Comprehensive video processing
from advanced_video_processor import process_video_with_enhancements

results = process_video_with_enhancements(
    video_path="input.mp4",
    output_dir="./processed",
    options={
        'add_subtitles': True,
        'add_emojis': True,
        'platforms': ['tiktok', 'instagram']
    }
)
```

## 🚀 Deployment Guide

### 🐳 Docker Deployment
```bash
# Build and run with Docker Compose
docker-compose up --build

# Or build individually
docker build -t smartclips-backend ./backend
docker run -p 8000:8000 smartclips-backend
```

### ☁️ Cloud Deployment

#### Frontend (Vercel/Netlify)
```bash
# Build for production
npm run build

# Deploy to Vercel
vercel --prod

# Environment variables for production
VITE_API_URL=https://your-backend-domain.com
VITE_SUPABASE_URL=your_production_supabase_url
```

#### Backend (Railway/Render/AWS)
```bash
# Production server
uvicorn main:app --host 0.0.0.0 --port 8000

# Environment variables
CORS_ORIGINS=["https://your-frontend-domain.com"]
DATABASE_URL=your_production_database_url
OPENAI_API_KEY=your_openai_key
CLOUDINARY_CLOUD_NAME=your_cloudinary_name
```

### 📊 Performance Optimization
- **CDN**: Cloudinary for global media delivery
- **Caching**: Redis for session and API response caching
- **Database**: PostgreSQL for production workloads
- **Monitoring**: Built-in FastAPI metrics and logging

## ⚠️ Current Status & Known Issues

### ✅ Working Features
- **Frontend-Backend Integration**: Fully functional API communication
- **Authentication**: Supabase + Google OAuth configured and tested
- **Video Processing**: Basic clipping and URL processing working
- **Advanced AI Features**: Subtitle generation, emoji integration, clipart overlays
- **Platform Optimization**: TikTok, Instagram, YouTube Shorts support

### 🔧 Development Notes
- Using `test_main.py` for simplified backend during development
- Advanced video processing requires additional ML dependencies
- Mock data responses for some endpoints during development
- CORS configured for local development (update for production)

### 🚨 Production Checklist
- [ ] Install full ML dependencies (`pip install -r requirements.txt`)
- [ ] Configure production database (PostgreSQL)
- [ ] Set up production environment variables
- [ ] Update CORS origins for production domains
- [ ] Configure CDN and caching layers
- [ ] Set up monitoring and logging

## 🤝 Contributing

We welcome contributions! Here's how to get started:

### Development Setup
1. **Fork** the repository
2. **Clone** your fork: `git clone https://github.com/your-username/smartclips.git`
3. **Create branch**: `git checkout -b feature/amazing-feature`
4. **Install dependencies**: `npm install && cd backend && pip install -r requirements.txt`
5. **Make changes** and test thoroughly
6. **Commit**: `git commit -m 'Add amazing feature'`
7. **Push**: `git push origin feature/amazing-feature`
8. **Create Pull Request**

### Contribution Guidelines
- Follow existing code style and conventions
- Add tests for new features
- Update documentation as needed
- Ensure all tests pass before submitting PR

## 📄 License & Credits

**License**: MIT License - see [LICENSE](LICENSE) file for details

**Credits**:
- Originally created with [Lovable](https://lovable.dev)
- Built with React, FastAPI, and modern web technologies
- AI integrations powered by OpenAI, ElevenLabs, and Hugging Face

---

## 📞 Support & Community

- **Issues**: [GitHub Issues](https://github.com/Anish-I/smartclips/issues)
- **Discussions**: [GitHub Discussions](https://github.com/Anish-I/smartclips/discussions)
- **Documentation**: This README and inline code comments

**Made with ❤️ for content creators worldwide**
