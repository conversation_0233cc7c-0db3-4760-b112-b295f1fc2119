import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, AlertCircle } from 'lucide-react';

const EnvCheck = () => {
  const envVars = [
    {
      name: 'VITE_SUPABASE_URL',
      value: import.meta.env.VITE_SUPABASE_URL,
      required: true,
      description: 'Supabase project URL'
    },
    {
      name: 'VITE_SUPABASE_ANON_KEY',
      value: import.meta.env.VITE_SUPABASE_ANON_KEY,
      required: true,
      description: 'Supabase anonymous key'
    },
    {
      name: 'VITE_GOOGLE_CLIENT_ID',
      value: import.meta.env.VITE_GOOGLE_CLIENT_ID,
      required: true,
      description: 'Google OAuth Client ID'
    },
    {
      name: 'VITE_API_URL',
      value: import.meta.env.VITE_API_URL,
      required: false,
      description: 'Backend API URL'
    }
  ];

  const getStatusIcon = (value: string | undefined, required: boolean) => {
    if (value) {
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    } else if (required) {
      return <XCircle className="h-4 w-4 text-red-500" />;
    } else {
      return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusBadge = (value: string | undefined, required: boolean) => {
    if (value) {
      return <Badge variant="default">Set</Badge>;
    } else if (required) {
      return <Badge variant="destructive">Missing</Badge>;
    } else {
      return <Badge variant="secondary">Optional</Badge>;
    }
  };

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-2">Environment Variables Check</h1>
          <p className="text-muted-foreground">Verify that all required environment variables are configured</p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Environment Variables Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {envVars.map((envVar) => (
                <div key={envVar.name} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(envVar.value, envVar.required)}
                    <div>
                      <div className="font-medium">{envVar.name}</div>
                      <div className="text-sm text-muted-foreground">{envVar.description}</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(envVar.value, envVar.required)}
                    {envVar.value && (
                      <span className="text-xs text-muted-foreground">
                        {envVar.value.length > 20 ? `${envVar.value.substring(0, 20)}...` : envVar.value}
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Configuration Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 text-sm">
              <div>
                <h3 className="font-semibold mb-2">Missing Environment Variables?</h3>
                <p className="text-muted-foreground mb-2">
                  Create a <code className="bg-muted px-1 rounded">.env</code> file in the root directory with:
                </p>
                <pre className="bg-muted p-3 rounded-lg overflow-auto">
{`# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Google OAuth Configuration
VITE_GOOGLE_CLIENT_ID=896094909946-1ksm30dbjg35q7c8a7kvc4g0h2kkq3ct.apps.googleusercontent.com

# Backend API URL
VITE_API_URL=http://localhost:8000`}
                </pre>
              </div>
              
              <div>
                <h3 className="font-semibold mb-2">After updating .env file:</h3>
                <ol className="list-decimal list-inside space-y-1 text-muted-foreground">
                  <li>Stop the development server (Ctrl+C)</li>
                  <li>Restart with <code className="bg-muted px-1 rounded">npm run dev</code></li>
                  <li>Refresh this page to see updated values</li>
                </ol>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Current Environment</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-xs bg-muted p-4 rounded-lg overflow-auto">
              {JSON.stringify({
                mode: import.meta.env.MODE,
                dev: import.meta.env.DEV,
                prod: import.meta.env.PROD,
                base_url: import.meta.env.BASE_URL,
                all_env_vars: Object.keys(import.meta.env).filter(key => key.startsWith('VITE_'))
              }, null, 2)}
            </pre>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default EnvCheck;
