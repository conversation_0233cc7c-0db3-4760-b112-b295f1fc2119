from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional
import json

app = FastAPI(title="SmartClips Test API", description="Test API for SmartClips integration")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For development
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Test models
class URLValidationRequest(BaseModel):
    url: str

class URLValidationResponse(BaseModel):
    valid: bool
    platform: Optional[str] = None
    video_id: Optional[str] = None
    error: Optional[str] = None

class VideoMetadataResponse(BaseModel):
    title: str
    duration: int
    uploader: str
    view_count: int
    platform: str
    thumbnail: str
    description: Optional[str] = None
    upload_date: Optional[str] = None

class URLProcessRequest(BaseModel):
    url: str
    min_duration: Optional[float] = 10.0
    max_duration: Optional[float] = 60.0
    quality: Optional[str] = "best"

class ProcessedURLResult(BaseModel):
    segments: List[dict]
    video_urls: List[str]

@app.get("/")
async def root():
    return {"message": "SmartClips Backend API is running!", "status": "healthy"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "smartclips-backend"}

@app.post("/validate-url", response_model=URLValidationResponse)
async def validate_video_url(request: URLValidationRequest):
    """Validate if a URL is from a supported platform"""
    url = request.url
    
    # Simple platform detection
    if "youtube.com" in url or "youtu.be" in url:
        return URLValidationResponse(valid=True, platform="youtube", video_id="test123")
    elif "tiktok.com" in url:
        return URLValidationResponse(valid=True, platform="tiktok", video_id="test456")
    elif "instagram.com" in url:
        return URLValidationResponse(valid=True, platform="instagram", video_id="test789")
    else:
        return URLValidationResponse(valid=False, error="Unsupported platform")

@app.post("/url-metadata", response_model=VideoMetadataResponse)
async def get_url_metadata(request: URLValidationRequest):
    """Get video metadata from URL"""
    url = request.url
    
    # Mock metadata based on platform
    if "youtube.com" in url or "youtu.be" in url:
        return VideoMetadataResponse(
            title="Sample YouTube Video",
            duration=180,
            uploader="Test Creator",
            view_count=10000,
            platform="youtube",
            thumbnail="https://img.youtube.com/vi/test123/maxresdefault.jpg",
            description="This is a test video description",
            upload_date="2024-01-01"
        )
    elif "tiktok.com" in url:
        return VideoMetadataResponse(
            title="Sample TikTok Video",
            duration=30,
            uploader="TikTok Creator",
            view_count=50000,
            platform="tiktok",
            thumbnail="https://example.com/tiktok-thumb.jpg",
            description="Viral TikTok content",
            upload_date="2024-01-02"
        )
    else:
        raise HTTPException(status_code=400, detail="Unsupported platform")

@app.post("/process-url", response_model=ProcessedURLResult)
async def process_video_url(request: URLProcessRequest):
    """Process video from URL and return clips"""
    
    # Mock processing result
    mock_segments = [
        {"start_time": 0, "end_time": 30, "text": "Engaging opening hook"},
        {"start_time": 45, "end_time": 75, "text": "Key insight moment"},
        {"start_time": 90, "end_time": 120, "text": "Call to action"}
    ]
    
    mock_clips = [
        "https://res.cloudinary.com/demo/video/upload/v1690380631/samples/clip-1.mp4",
        "https://res.cloudinary.com/demo/video/upload/v1690380631/samples/clip-2.mp4",
        "https://res.cloudinary.com/demo/video/upload/v1690380631/samples/clip-3.mp4"
    ]
    
    return ProcessedURLResult(
        segments=mock_segments,
        video_urls=mock_clips
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
