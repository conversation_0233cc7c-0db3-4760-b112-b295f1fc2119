"""
Advanced Video Processing Module for SmartClips
Provides comprehensive video enhancement with subtitles, emojis, clipart, and short-form content creation
Updated with enhanced TikTok-style subtitle system, PROPER WORD SPACING, and AI-POWERED FEATURES
"""

import os
import re
import json
import tempfile
import subprocess
import logging
from typing import List, Dict, Any, Tuple, Optional
from pathlib import Path
import requests
from datetime import datetime, timedelta

# Core processing libraries
import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import librosa
from moviepy.editor import VideoFileClip, AudioFileClip, ImageClip, CompositeVideoClip, TextClip, ColorClip

# NLP and AI libraries
import nltk
import spacy
from textblob import TextBlob
import emoji
import openai
from transformers import pipeline

# Subtitle processing
import pysrt
import whisper_timestamped as whisper

logger = logging.getLogger(__name__)

class AdvancedVideoProcessor:
    """
    Comprehensive video processor that adds subtitles, emojis, clipart, and creates short-form content
    Enhanced with AI-powered emoji selection and importance detection
    """
    
    def __init__(self, openai_api_key: str = None, temp_dir: str = None):
        self.openai_api_key = openai_api_key or os.getenv("OPENAI_API_KEY")
        self.temp_dir = temp_dir or tempfile.gettempdir()
        
        # Initialize OpenAI client for AI-powered features
        if self.openai_api_key:
            self.openai_client = openai.OpenAI(api_key=self.openai_api_key)
        else:
            self.openai_client = None

        # Initialize NLP models
        self._init_nlp_models()
        
        # Initialize emotion analysis
        self.emotion_analyzer = pipeline("text-classification", 
                                       model="j-hartmann/emotion-english-distilroberta-base")
        
        # Emoji mapping for emotions (fallback when AI unavailable)
        self.emotion_emojis = {
            'joy': ['😊', '😄', '🎉', '✨', '💫'],
            'sadness': ['😢', '😔', '💔', '😞'],
            'anger': ['😠', '😡', '🔥', '💢'],
            'fear': ['😨', '😰', '😱', '🙈'],
            'surprise': ['😲', '😮', '🤯', '😯'],
            'disgust': ['🤢', '😷', '🤮', '😖'],
            'love': ['❤️', '💕', '😍', '🥰', '💖']
        }
        
        # Platform-specific settings
        self.platform_settings = {
            'tiktok': {'aspect_ratio': (9, 16), 'max_duration': 60, 'resolution': (1080, 1920)},
            'instagram': {'aspect_ratio': (9, 16), 'max_duration': 90, 'resolution': (1080, 1920)},
            'youtube_shorts': {'aspect_ratio': (9, 16), 'max_duration': 60, 'resolution': (1080, 1920)},
            'twitter': {'aspect_ratio': (16, 9), 'max_duration': 140, 'resolution': (1280, 720)}
        }

        # Updated TikTok-style subtitle configurations with PROPER SPACING
        self.tiktok_styles = {
            'clean_modern': {
                'fontsize': 25,
                'font': 'Impact',
                'color': '#FFFFFF',
                'stroke_color': '#000000',
                'stroke_width': 1,
                'position': ('center', 0.4),
                'max_words_per_line': 3,
                'text_transform': 'upper',
                'animation': 'zoom_bounce',
                'accent_color': '#FFD700',
                'accent_frequency': 0.3,
                'line_spacing': -5,
                'word_spacing': 5,
                'character_spacing': 1,
                'letter_spacing': 1,
                'kerning': 1
            },
            'viral_style': {
                'fontsize': 30,
                'font': 'Impact',
                'color': '#FFFFFF',
                'stroke_color': '#000000',
                'stroke_width': 2,
                'position': ('center', 0.38),
                'max_words_per_line': 4,
                'text_transform': 'upper',
                'animation': 'bounce_in',
                'accent_color': '#00FF00',
                'accent_frequency': 0.4,
                'line_spacing': -5,
                'word_spacing': 7,
                'character_spacing': 1,
                'letter_spacing': 1,
                'kerning': 1
            },
            'modern': {
                'fontsize': 26,
                'font': 'Arial-Black',
                'color': '#FFFFFF',
                'stroke_color': '#000000',
                'stroke_width': 2,
                'position': ('center', 0.35),
                'max_words_per_line': 5,
                'text_transform': 'upper',
                'animation': 'fade_slide',
                'accent_color': '#FFD700',
                'accent_frequency': 0.25,
                'line_spacing': -3,
                'word_spacing': 3,
                'character_spacing': 1,
                'letter_spacing': 1,
                'kerning': 1
            }
        }
    
    def _init_nlp_models(self):
        """Initialize NLP models and download required data"""
        try:
            # Download NLTK data
            nltk.download('punkt', quiet=True)
            nltk.download('averaged_perceptron_tagger', quiet=True)
            nltk.download('wordnet', quiet=True)
            nltk.download('stopwords', quiet=True)

            # Load spaCy model
            try:
                self.nlp = spacy.load("en_core_web_sm")
            except OSError:
                logger.warning("spaCy model not found. Install with: python -m spacy download en_core_web_sm")
                self.nlp = None

        except Exception as e:
            logger.error(f"Error initializing NLP models: {e}")
            self.nlp = None

    def extract_transcript_with_timing(self, video_path: str) -> List[Dict[str, Any]]:
        """
        Extract transcript with precise word-level timing using Whisper
        """
        try:
            # Load Whisper model
            model = whisper.load_model("base")

            # Transcribe with timestamps
            result = whisper.transcribe(model, video_path, language="en")

            # Extract word-level timestamps
            transcript_data = []
            for segment in result['segments']:
                for word_info in segment.get('words', []):
                    transcript_data.append({
                        'word': word_info['text'].strip(),
                        'start': word_info['start'],
                        'end': word_info['end'],
                        'confidence': word_info.get('confidence', 1.0)
                    })
            
            return transcript_data

        except Exception as e:
            logger.error(f"Error extracting transcript: {e}")
            return []

    def analyze_content_with_ai(self, transcript_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Use AI to analyze transcript for emojis and importance detection
        """
        enhanced_transcript = []

        # Group words into segments for AI analysis
        segments = self._group_words_into_segments(transcript_data, max_words=15)

        for segment in segments:
            segment_text = ' '.join([word['word'] for word in segment['words']])

            try:
                # AI analysis for emojis and importance
                ai_analysis = self._get_ai_content_analysis(segment_text)

                # Apply AI insights to words in segment
                for i, word_data in enumerate(segment['words']):
                    enhanced_word = word_data.copy()

                    # Add AI-suggested emoji to the last word if provided
                    if i == len(segment['words']) - 1 and ai_analysis.get('emoji'):
                        enhanced_word['emoji'] = ai_analysis['emoji']
                        enhanced_word['emoji_confidence'] = ai_analysis.get('emoji_confidence', 0.8)

                    # Mark important segments
                    if ai_analysis.get('is_important', False):
                        enhanced_word['is_important'] = True
                        enhanced_word['importance_reason'] = ai_analysis.get('importance_reason', '')
                        enhanced_word['importance_score'] = ai_analysis.get('importance_score', 0.7)

                    # Add emotional context
                    if ai_analysis.get('emotion'):
                        enhanced_word['ai_emotion'] = ai_analysis['emotion']
                        enhanced_word['emotion_intensity'] = ai_analysis.get('emotion_intensity', 0.5)

                    enhanced_transcript.append(enhanced_word)

            except Exception as e:
                logger.error(f"Error in AI analysis for segment: {e}")
                # Fallback to original data
                enhanced_transcript.extend(segment['words'])

        return enhanced_transcript

    def _get_ai_content_analysis(self, text: str) -> Dict[str, Any]:
        """
        Get AI analysis for emoji suggestion and importance detection
        """
        if not self.openai_api_key or not self.openai_client:
            logger.warning("OpenAI API key not provided, using fallback analysis")
            return self._fallback_content_analysis(text)

        try:
            prompt = f"""
            Analyze this spoken text segment for video subtitles:
            "{text}"

            Provide a JSON response with:
            1. "emoji": A single relevant emoji that best represents the content/emotion (or null if none fits)
            2. "emoji_confidence": Confidence score 0-1 for emoji relevance
            3. "is_important": Boolean - true if this text contains important information, emphasis, or key points
            4. "importance_score": Score 0-1 indicating how important/emphatic this text is
            5. "importance_reason": Brief reason why it's important (e.g., "key point", "emotional peak", "call to action")
            6. "emotion": Primary emotion detected (joy, excitement, surprise, emphasis, etc.)
            7. "emotion_intensity": How intense the emotion is (0-1)

            Consider important:
            - Key information or facts
            - Emotional peaks or emphasis
            - Calls to action
            - Surprising or impactful statements
            - Strong opinions or conclusions

            Return only valid JSON.
            """

            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=200,
                temperature=0.3
            )

            content = response.choices[0].message.content.strip()

            # Parse JSON response
            import json
            analysis = json.loads(content)

            # Validate and clean response
            return {
                'emoji': analysis.get('emoji'),
                'emoji_confidence': min(1.0, max(0.0, analysis.get('emoji_confidence', 0.5))),
                'is_important': bool(analysis.get('is_important', False)),
                'importance_score': min(1.0, max(0.0, analysis.get('importance_score', 0.0))),
                'importance_reason': analysis.get('importance_reason', ''),
                'emotion': analysis.get('emotion', ''),
                'emotion_intensity': min(1.0, max(0.0, analysis.get('emotion_intensity', 0.5)))
            }

        except Exception as e:
            logger.error(f"Error in OpenAI analysis: {e}")
            return self._fallback_content_analysis(text)

    def _fallback_content_analysis(self, text: str) -> Dict[str, Any]:
        """
        Fallback analysis when AI is not available
        """
        # Simple keyword-based importance detection
        important_keywords = [
            'important', 'key', 'crucial', 'essential', 'remember', 'listen',
            'amazing', 'incredible', 'wow', 'unbelievable', 'shocking',
            'must', 'should', 'need to', 'have to', 'critical',
            'finally', 'conclusion', 'result', 'outcome'
        ]
        
        text_lower = text.lower()
        is_important = any(keyword in text_lower for keyword in important_keywords)

        # Simple emoji mapping
        emoji_map = {
            'happy': '😊', 'sad': '😢', 'angry': '😠', 'surprised': '😲',
            'love': '❤️', 'money': '💰', 'success': '🎉', 'fire': '🔥',
            'good': '👍', 'bad': '👎', 'amazing': '🤩', 'wow': '😮'
        }

        emoji = None
        for keyword, emoji_char in emoji_map.items():
            if keyword in text_lower:
                emoji = emoji_char
                break

        return {
            'emoji': emoji,
            'emoji_confidence': 0.6 if emoji else 0.0,
            'is_important': is_important,
            'importance_score': 0.7 if is_important else 0.3,
            'importance_reason': 'keyword_match' if is_important else '',
            'emotion': 'neutral',
            'emotion_intensity': 0.5
        }

    def _group_words_into_segments(self, transcript_data: List[Dict[str, Any]], max_words: int = 15) -> List[Dict[str, Any]]:
        """Group words into segments for AI analysis"""
        segments = []
        current_segment = {'words': [], 'start': None, 'end': None}

        for word_data in transcript_data:
            if current_segment['start'] is None:
                current_segment['start'] = word_data['start']

            current_segment['words'].append(word_data)
            current_segment['end'] = word_data['end']

            # Create segment when we have enough words or reach strong punctuation
            if (len(current_segment['words']) >= max_words or
                word_data['word'].endswith(('.', '!', '?'))):
                segments.append(current_segment)
                current_segment = {'words': [], 'start': None, 'end': None}

        # Add remaining words
        if current_segment['words']:
            segments.append(current_segment)

        return segments

    def analyze_emotions_and_add_emojis(self, transcript_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Fallback emotion analysis and emoji addition (for backward compatibility)
        """
        enhanced_transcript = []

        # Group words into sentences for better emotion analysis
        sentences = self._group_words_into_sentences(transcript_data)

        for sentence_data in sentences:
            sentence_text = ' '.join([word['word'] for word in sentence_data['words']])

            # Analyze emotion
            emotion_result = self.emotion_analyzer(sentence_text)[0]
            emotion = emotion_result['label'].lower()
            confidence = emotion_result['score']

            # Add emoji if confidence is high enough
            if confidence > 0.7 and emotion in self.emotion_emojis:
                emoji_choice = np.random.choice(self.emotion_emojis[emotion])

                # Add emoji to the last word of the sentence
                if sentence_data['words']:
                    sentence_data['words'][-1]['emoji'] = emoji_choice
                    sentence_data['words'][-1]['emotion'] = emotion
                    sentence_data['words'][-1]['emotion_confidence'] = confidence

            enhanced_transcript.extend(sentence_data['words'])

        return enhanced_transcript

    def _group_words_into_sentences(self, transcript_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Group words into sentences for better analysis"""
        sentences = []
        current_sentence = {'words': [], 'start': None, 'end': None}

        for word_data in transcript_data:
            word = word_data['word']

            if current_sentence['start'] is None:
                current_sentence['start'] = word_data['start']

            current_sentence['words'].append(word_data)
            current_sentence['end'] = word_data['end']

            # Check for sentence endings
            if word.endswith(('.', '!', '?')) or len(current_sentence['words']) > 15:
                sentences.append(current_sentence)
                current_sentence = {'words': [], 'start': None, 'end': None}

        # Add remaining words as a sentence
        if current_sentence['words']:
            sentences.append(current_sentence)

        return sentences

    def create_emoji_overlay_clips(self, transcript_data: List[Dict[str, Any]], video_size: Tuple[int, int]) -> List:
        """
        Create emoji overlay clips that appear on the video itself
        """
        emoji_clips = []

        for word_data in transcript_data:
            if word_data.get('emoji'):
                try:
                    # Create emoji text clip
                    emoji_clip = TextClip(
                        word_data['emoji'],
                        fontsize=80,  # Large emoji size
                        color='white',
                        font='Arial'  # Better emoji support
                    ).set_start(word_data['start']).set_duration(2.0)  # Show for 2 seconds

                    # Position emoji in different locations to avoid overlap
                    positions = [
                        ('right', 'top'),       # Top right
                        ('left', 'top'),        # Top left
                        ('right', 'bottom'),    # Bottom right
                        ('left', 'bottom'),     # Bottom left
                        ('center', 'top')       # Top center
                    ]

                    # Choose position based on emoji timing to avoid overlap
                    import random
                    random.seed(int(word_data['start'] * 100))  # Consistent positioning
                    position = random.choice(positions)

                    # Add some random offset for natural feel
                    if position[0] == 'center':
                        x_offset = random.randint(-100, 100)
                        emoji_clip = emoji_clip.set_position((video_size[0]//2 + x_offset, 50))
                    else:
                        emoji_clip = emoji_clip.set_position(position)

                    # Add subtle animation
                    emoji_clip = self._animate_emoji(emoji_clip)

                    emoji_clips.append(emoji_clip)

                except Exception as e:
                    logger.error(f"Error creating emoji overlay: {e}")
                    continue

        return emoji_clips

    def _animate_emoji(self, emoji_clip):
        """Add subtle animation to emoji"""
        try:
            # Bounce in effect
            def size_func(t):
                if t < 0.3:
                    return 0.5 + 0.5 * (t / 0.3) + 0.2 * np.sin(10 * t)
                return 1.0

            emoji_clip = emoji_clip.resize(size_func)
            emoji_clip = emoji_clip.crossfadein(0.3).crossfadeout(0.5)

            return emoji_clip
        except Exception as e:
            logger.error(f"Error animating emoji: {e}")
            return emoji_clip.crossfadein(0.2).crossfadeout(0.3)

    def detect_important_text_segments(self, transcript_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        AI-powered detection of important/emphatic text segments for accent coloring.
        """
        important_segments = []

        # Group words into sentences for analysis
        sentences = self._group_words_into_sentences(transcript_data)

        for sentence_data in sentences:
            sentence_text = ' '.join([word['word'] for word in sentence_data['words']])
            importance_score = 0
            importance_type = 'normal'
            accent_color = '#00FF00'  # Default green

            # Check for AI-detected importance first
            ai_important_words = [word for word in sentence_data['words'] if word.get('is_important', False)]
            if ai_important_words:
                importance_score = max([word.get('importance_score', 0.7) for word in ai_important_words])
                importance_type = 'ai_detected'
                accent_color = '#00FF00'  # Bright green for AI-detected importance

            # Emotion analysis
            try:
                emotion_result = self.emotion_analyzer(sentence_text)[0]
                emotion = emotion_result['label'].lower()
                confidence = emotion_result['score']
                if confidence > 0.6:
                    if emotion in ['joy', 'surprise']:
                        importance_score += 0.6
                        importance_type = 'positive_emotion'
                        accent_color = '#FFD700'  # Gold
                    elif emotion in ['anger', 'fear']:
                        importance_score += 0.5
                        importance_type = 'intense_emotion'
                        accent_color = '#FF4500'  # Orange-Red
            except Exception:
                pass

            # NLP: Key phrases/entities
            if self.nlp:
                doc = self.nlp(sentence_text)
                if any(ent.label_ in ['PERSON', 'ORG', 'GPE'] for ent in doc.ents):
                    importance_score += 0.4
                    importance_type = 'named_entity'
                    accent_color = '#00CED1'  # Cyan

            # Emphasis: ALL CAPS, exclamation, repetition, keywords
            if '!' in sentence_text or any(word.isupper() and len(word) > 2 for word in sentence_text.split()):
                importance_score += 0.7
                importance_type = 'emphasis'
                accent_color = '#00FF00'  # Green

            important_keywords = ['amazing', 'incredible', 'awesome', 'fantastic', 'important', 'must', 'best', 'winner']
            if any(keyword in sentence_text.lower() for keyword in important_keywords):
                importance_score += 0.5
                importance_type = 'keyword_emphasis'
                accent_color = '#FF69B4'  # Pink

            if importance_score >= 0.5:
                important_segments.append({
                    'text': sentence_text,
                    'start': sentence_data['start'],
                    'end': sentence_data['end'],
                    'duration': sentence_data['end'] - sentence_data['start'],
                    'importance_score': importance_score,
                    'importance_type': importance_type,
                    'accent_color': accent_color,
                    'words': sentence_data['words']
                })

        return important_segments

    def _format_text_with_proper_spacing(self, text: str, style_config: Dict[str, Any]) -> str:
        """
        Format text with proper word spacing using special spacing characters
        """
        # Apply text transform
        transform = style_config.get('text_transform', 'none')
        if transform == 'upper':
            text = text.upper()
        elif transform == 'title':
            text = text.title()
        elif transform == 'lower':
            text = text.lower()

        # Clean and split into words
        words = text.strip().split()
        max_words = style_config.get('max_words_per_line', 4)

        # Create lines with proper spacing
        lines = []
        current_line = []

        for word in words:
            current_line.append(word)

            if len(current_line) >= max_words:
                # Join words with extra spaces for better spacing
                spaced_line = self._add_word_spacing(current_line, style_config)
                lines.append(spaced_line)
                current_line = []

        # Add remaining words
        if current_line:
            spaced_line = self._add_word_spacing(current_line, style_config)
            lines.append(spaced_line)

        # Join lines
        return '\n'.join(lines)

    def _add_word_spacing(self, words: List[str], style_config: Dict[str, Any]) -> str:
        """
        Add proper spacing between words using unicode spacing characters
        """
        word_spacing = style_config.get('word_spacing', 8)

        # Use em spaces and en spaces for better word separation
        if word_spacing >= 10:
            # Heavy spacing - use multiple em spaces
            spacer = '   '  # 3 regular spaces
        elif word_spacing >= 6:
            # Medium spacing - use em space + regular space
            spacer = '  '   # 2 regular spaces
        else:
            # Light spacing - use en space
            spacer = ' '    # 1 regular space

        return spacer.join(words)

    def _apply_tiktok_animation(self, clip, animation_type: str):
        """
        Apply TikTok-style animations to text clips
        """
        try:
            if animation_type == 'bounce_in':
                # Bounce in effect
                clip = clip.crossfadein(0.2)
                def bounce_func(t):
                    return 1 + 0.15 * np.exp(-4*t) * np.sin(12*t) if t < 0.6 else 1
                clip = clip.resize(bounce_func)

            elif animation_type == 'fade_slide':
                # Fade in with slight slide - simplified version
                clip = clip.crossfadein(0.3).crossfadeout(0.2)

            elif animation_type == 'zoom_bounce':
                # Zoom in with bounce
                def zoom_func(t):
                    if t < 0.5:
                        base_scale = 0.8 + 0.2 * (t / 0.5)
                        bounce = 0.05 * np.sin(20 * t) if t < 0.3 else 0
                        return base_scale + bounce
                    return 1.0
                clip = clip.resize(zoom_func)

            elif animation_type == 'fade_in_out':
                # Simple fade in and out
                clip = clip.crossfadein(0.3).crossfadeout(0.3)

            return clip
        except Exception as e:
            logger.error(f"Error applying animation {animation_type}: {e}")
            # Return clip with simple fade as fallback
            return clip.crossfadein(0.2).crossfadeout(0.2)

    def _create_single_color_text(self, text: str, style_config: Dict[str, Any],
                                start_time: float, duration: float, video_size: Tuple[int, int]):
        """
        Create single color text clip with PROPER SPACING
        """
        # Apply proper text formatting if not already done
        if not any(spacing_char in text for spacing_char in ['  ', '   ']):
            formatted_text = self._format_text_with_proper_spacing(text, style_config)
        else:
            formatted_text = text

        txt_clip = TextClip(
            formatted_text,
            fontsize=style_config['fontsize'],
            color=style_config['color'],
            stroke_color=style_config['stroke_color'],
            stroke_width=style_config['stroke_width'],
            font=style_config['font'],
            method='caption',
            size=(int(video_size[0] * 0.9), None),
            align='center',
            interline=style_config.get('line_spacing', -5),  # Better line spacing
            kerning=style_config.get('kerning', 1)  # Better letter spacing
        ).set_start(start_time).set_duration(duration)

        # Position the text
        position = style_config['position']
        if isinstance(position[1], float):
            y_pos = int(video_size[1] * position[1])
            txt_clip = txt_clip.set_position(('center', y_pos))
        else:
            txt_clip = txt_clip.set_position(position)

        return txt_clip

    def create_ai_enhanced_subtitles(self, video_path: str, transcript_data: List[Dict[str, Any]],
                                   output_path: str, style: str = "clean_modern",
                                   enable_emoji_overlays: bool = True,
                                   enable_importance_highlighting: bool = True) -> str:
        """
        Create AI-enhanced video with emoji overlays and importance highlighting
        """
        try:
            video = VideoFileClip(video_path)
            subtitle_clips = []
            emoji_clips = []

            # Get style configuration
            current_style = self.tiktok_styles.get(style, self.tiktok_styles['clean_modern'])

            # Detect important segments for color highlighting
            important_segments = []
            if enable_importance_highlighting:
                important_segments = self.detect_important_text_segments(transcript_data)

            # Create emoji overlays if enabled
            if enable_emoji_overlays:
                emoji_clips = self.create_emoji_overlay_clips(transcript_data, video.size)

            # Create subtitle chunks
            subtitle_chunks = self._create_subtitle_chunks(transcript_data, current_style)

            # Create subtitle clips with AI-powered color highlighting
            for chunk in subtitle_chunks:
                base_text = chunk['text'].strip()
                chunk_start = chunk['start']
                chunk_end = chunk['end']

                # Check if this chunk overlaps with any important segment
                is_important = False
                accent_color = current_style['color']

                if enable_importance_highlighting:
                    for segment in important_segments:
                        if (chunk_start <= segment['end'] and chunk_end >= segment['start']):
                            if any(word in base_text.lower() for word in segment['text'].lower().split()):
                                is_important = True
                                accent_color = segment['accent_color']
                                break

                # Create subtitle clip with appropriate color
                style_to_use = current_style.copy()
                if is_important:
                    style_to_use['color'] = accent_color

                txt_clip = self._create_single_color_text(
                    base_text, style_to_use, chunk['start'], 
                    chunk['duration'], video.size
                )

                # Apply animations
                txt_clip = self._apply_tiktok_animation(txt_clip, current_style['animation'])
                subtitle_clips.append(txt_clip)

            # Composite all clips
            all_clips = [video] + subtitle_clips + emoji_clips
            final_video = CompositeVideoClip(all_clips)

            # Write the final video
            final_video.write_videofile(
                output_path,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True
            )

            # Clean up
            video.close()
            final_video.close()
            for clip in subtitle_clips + emoji_clips:
                if hasattr(clip, 'close'):
                    clip.close()

            return output_path

        except Exception as e:
            logger.error(f"Error creating AI-enhanced subtitles: {e}")
            raise

    def _create_subtitle_chunks(self, transcript_data: List[Dict[str, Any]], style_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Create subtitle chunks from transcript data with style-aware formatting"""
        chunks = []
        current_chunk = {'words': [], 'start': None, 'end': None, 'text': '', 'word_data': []}
        max_words = style_config.get('max_words_per_line', 4)

        for word_data in transcript_data:
            if current_chunk['start'] is None:
                current_chunk['start'] = word_data['start']

            current_chunk['words'].append(word_data)
            current_chunk['word_data'].append(word_data)
            current_chunk['end'] = word_data['end']
            current_chunk['text'] += word_data['word'] + ' '

            # Create chunk when we have enough words or reach punctuation
            word_count = len(current_chunk['words'])
            ends_with_punctuation = word_data['word'].endswith(('.', '!', '?', ','))

            if word_count >= max_words or ends_with_punctuation:
                current_chunk['text'] = current_chunk['text'].strip()
                current_chunk['duration'] = current_chunk['end'] - current_chunk['start']
                chunks.append(current_chunk)

                current_chunk = {'words': [], 'start': None, 'end': None, 'text': '', 'word_data': []}

        # Add remaining words
        if current_chunk['words']:
            current_chunk['text'] = current_chunk['text'].strip()
            current_chunk['duration'] = current_chunk['end'] - current_chunk['start']
            chunks.append(current_chunk)

        return chunks

    # Legacy methods for backward compatibility
    def create_animated_subtitles(self, video_path: str, transcript_data: List[Dict[str, Any]],
                                output_path: str, style: str = "clean_modern",
                                enable_multicolor: bool = False) -> str:
        """
        Legacy method for backward compatibility - redirects to AI-enhanced version
        """
        return self.create_ai_enhanced_subtitles(
            video_path,
            transcript_data,
            output_path,
            style,
            enable_emoji_overlays=False,  # Disable for legacy compatibility
            enable_importance_highlighting=enable_multicolor
        )

    def extract_key_nouns(self, transcript_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Extract key nouns for clipart overlay
        """
        if not self.nlp:
            return []

        # Combine all words into text
        full_text = ' '.join([word['word'] for word in transcript_data])

        # Process with spaCy
        doc = self.nlp(full_text)

        # Extract important nouns
        key_nouns = []
        for token in doc:
            if (token.pos_ == 'NOUN' and 
                not token.is_stop and 
                len(token.text) > 3 and
                token.text.isalpha()):

                # Find timing for this noun in transcript
                for word_data in transcript_data:
                    if token.text.lower() in word_data['word'].lower():
                        key_nouns.append({
                            'noun': token.text,
                            'start': word_data['start'],
                            'end': word_data['end'],
                            'lemma': token.lemma_
                        })
                        break

        # Remove duplicates and sort by importance
        unique_nouns = {}
        for noun_data in key_nouns:
            key = noun_data['noun'].lower()
            if key not in unique_nouns:
                unique_nouns[key] = noun_data

        return list(unique_nouns.values())

    def search_clipart_images(self, nouns: List[Dict[str, Any]]) -> Dict[str, str]:
        """
        Search for clipart images for key nouns using free APIs
        """
        clipart_urls = {}

        for noun_data in nouns:
            noun = noun_data['noun']
            try:
                # Use placeholder images
                clipart_urls[noun] = f"https://via.placeholder.com/200x200/FF6B6B/FFFFFF?text={noun}"
                
            except Exception as e:
                logger.error(f"Error searching clipart for {noun}: {e}")
                # Fallback to placeholder
                clipart_urls[noun] = f"https://via.placeholder.com/200x200/4ECDC4/FFFFFF?text={noun}"

        return clipart_urls

    def download_clipart_image(self, url: str, noun: str) -> str:
        """Download clipart image and return local path"""
        try:
            response = requests.get(url, timeout=10)
            response.raise_for_status()

            # Save to temp directory
            image_path = os.path.join(self.temp_dir, f"clipart_{noun}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")

            with open(image_path, 'wb') as f:
                f.write(response.content)

            return image_path

        except Exception as e:
            logger.error(f"Error downloading clipart for {noun}: {e}")
            return None

    def add_clipart_overlays(self, video_path: str, nouns_data: List[Dict[str, Any]],
                           clipart_urls: Dict[str, str], output_path: str) -> str:
        """
        Add clipart overlays for key nouns
        """
        try:
            video = VideoFileClip(video_path)
            overlay_clips = []

            for noun_data in nouns_data:
                noun = noun_data['noun']
                if noun in clipart_urls:
                    # Download clipart image
                    image_path = self.download_clipart_image(clipart_urls[noun], noun)

                    if image_path and os.path.exists(image_path):
                        # Create image clip
                        img_clip = (ImageClip(image_path)
                                  .set_duration(2.0)  # Show for 2 seconds
                                  .set_start(noun_data['start'])
                                  .resize(height=150)  # Resize to appropriate size
                                  .set_position(('right', 'top'))  # Position in top-right
                                  .set_opacity(0.8))  # Semi-transparent

                        # Add fade in/out effects
                        img_clip = img_clip.crossfadein(0.3).crossfadeout(0.3)

                        overlay_clips.append(img_clip)

            # Composite video with overlays
            if overlay_clips:
                final_video = CompositeVideoClip([video] + overlay_clips)
                final_video.write_videofile(output_path, codec='libx264', audio_codec='aac')

                # Clean up
                final_video.close()
                for clip in overlay_clips:
                    clip.close()
            else:
                # No overlays, just copy the video
                video.write_videofile(output_path, codec='libx264', audio_codec='aac')

            video.close()
            return output_path

        except Exception as e:
            logger.error(f"Error adding clipart overlays: {e}")
            raise

    def create_short_form_content(self, video_path: str, platform: str = 'tiktok',
                                max_clips: int = 3) -> List[str]:
        """
        Create short-form content optimized for different platforms
        """
        try:
            video = VideoFileClip(video_path)
            platform_config = self.platform_settings.get(platform, self.platform_settings['tiktok'])

            # Analyze video for best segments
            segments = self._analyze_video_segments(video, platform_config['max_duration'])

            output_paths = []

            for i, segment in enumerate(segments[:max_clips]):
                try:
                    # Extract segment
                    clip = video.subclip(segment['start'], segment['end'])

                    # Apply platform-specific optimizations
                    optimized_clip = self._optimize_for_platform(clip, platform_config)

                    # Add short-form effects
                    enhanced_clip = self._add_short_form_effects(optimized_clip, platform)

                    # Save clip
                    output_path = os.path.join(self.temp_dir, f"short_form_{platform}_{i+1}.mp4")
                    enhanced_clip.write_videofile(
                        output_path,
                        codec='libx264',
                        audio_codec='aac',
                        verbose=False,
                        logger=None
                    )

                    output_paths.append(output_path)

                    # Clean up
                    clip.close()
                    optimized_clip.close()
                    enhanced_clip.close()

                except Exception as e:
                    logger.error(f"Error processing segment {i}: {e}")
                    continue

            video.close()
            return output_paths

        except Exception as e:
            logger.error(f"Error creating short-form content: {e}")
            return []

    def _analyze_video_segments(self, video: VideoFileClip, max_duration: int) -> List[Dict[str, float]]:
        """
        Analyze video to find the most engaging segments
        """
        segments = []
        duration = video.duration

        # Simple segmentation - divide video into potential clips
        segment_length = min(max_duration, 30)  # Max 30 seconds per segment

        for start in range(0, int(duration), segment_length // 2):  # 50% overlap
            end = min(start + segment_length, duration)
            if end - start >= 10:  # Minimum 10 seconds
                segments.append({
                    'start': start,
                    'end': end,
                    'score': self._calculate_segment_score(video, start, end)
                })

        # Sort by score and return top segments
        segments.sort(key=lambda x: x['score'], reverse=True)
        return segments

    def _calculate_segment_score(self, video: VideoFileClip, start: float, end: float) -> float:
        """
        Calculate engagement score for a video segment
        """
        try:
            # Simple scoring based on position in video
            video_duration = video.duration
            segment_center = (start + end) / 2
            middle_of_video = video_duration / 2

            # Higher score for segments closer to middle
            distance_from_middle = abs(segment_center - middle_of_video)
            max_distance = video_duration / 2
            score = 1.0 - (distance_from_middle / max_distance)

            # Add some randomness
            import random
            score += random.random() * 0.3

            return max(0.1, min(1.0, score))

        except Exception as e:
            logger.error(f"Error calculating segment score: {e}")
            return 0.5

    def _optimize_for_platform(self, clip: VideoFileClip, platform_config: Dict) -> VideoFileClip:
        """
        Optimize video clip for specific platform requirements
        """
        target_resolution = platform_config['resolution']

        # Resize and crop to target aspect ratio
        current_w, current_h = clip.size
        target_w, target_h = target_resolution

        # Calculate scaling to maintain aspect ratio
        scale_w = target_w / current_w
        scale_h = target_h / current_h
        scale = max(scale_w, scale_h)

        # Resize video
        resized_clip = clip.resize(scale)

        # Crop to exact dimensions
        final_clip = resized_clip.crop(
            x_center=resized_clip.w/2,
            y_center=resized_clip.h/2,
            width=target_w,
            height=target_h
        )

        return final_clip

    def _add_short_form_effects(self, clip: VideoFileClip, platform: str) -> VideoFileClip:
        """
        Add platform-specific effects for short-form content
        """
        if platform == 'tiktok':
            return self._add_tiktok_effects(clip)
        elif platform == 'instagram':
            return self._add_instagram_effects(clip)
        else:
            return clip

    def _add_tiktok_effects(self, clip: VideoFileClip) -> VideoFileClip:
        """Add TikTok-style effects"""
        zoomed_clip = clip.resize(lambda t: 1 + 0.02 * np.sin(2 * np.pi * t / clip.duration))
        return zoomed_clip

    def _add_instagram_effects(self, clip: VideoFileClip) -> VideoFileClip:
        """Add Instagram-style effects"""
        faded_clip = clip.crossfadein(0.5).crossfadeout(0.5)
        return faded_clip

    def process_video_comprehensive(self, video_path: str, output_dir: str,
                                  options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Main function that processes video with all AI enhancements
        """
        if options is None:
            options = {}

        # Default options
        add_subtitles = options.get('add_subtitles', True)
        add_emojis = options.get('add_emojis', True)
        add_clipart = options.get('add_clipart', False)
        create_short_form = options.get('create_short_form', False)
        platforms = options.get('platforms', ['tiktok', 'instagram'])
        subtitle_style = options.get('subtitle_style', 'clean_modern')
        max_short_clips = options.get('max_short_clips', 3)
        use_ai_features = options.get('use_ai_features', False)

        results = {
            'original_video': video_path,
            'processed_videos': {},
            'short_form_clips': {},
            'metadata': {},
            'processing_time': 0
        }

        start_time = datetime.now()

        try:
            logger.info(f"Starting comprehensive AI video processing for: {video_path}")

            # Step 1: Extract transcript with timing
            logger.info("Extracting transcript with timing...")
            transcript_data = self.extract_transcript_with_timing(video_path)

            if not transcript_data:
                raise Exception("Failed to extract transcript")

            # Step 2: AI analysis or fallback emotion analysis
            if use_ai_features and self.openai_api_key:
                logger.info("Analyzing content with AI for emojis and importance...")
                transcript_data = self.analyze_content_with_ai(transcript_data)
            elif add_emojis:
                logger.info("Analyzing emotions and adding emojis...")
                transcript_data = self.analyze_emotions_and_add_emojis(transcript_data)

            results['metadata']['transcript'] = transcript_data

            # Step 3: Extract key nouns for clipart (optional)
            clipart_urls = {}
            if add_clipart:
                logger.info("Extracting key nouns for clipart...")
                key_nouns = self.extract_key_nouns(transcript_data)
                clipart_urls = self.search_clipart_images(key_nouns)
                results['metadata']['key_nouns'] = key_nouns
                results['metadata']['clipart_urls'] = clipart_urls

            # Step 4: Create AI-enhanced video with subtitles
            if add_subtitles:
                logger.info("Creating AI-enhanced animated subtitles...")
                subtitled_path = os.path.join(output_dir, "video_with_ai_subtitles.mp4")
                self.create_ai_enhanced_subtitles(
                    video_path,
                    transcript_data,
                    subtitled_path,
                    subtitle_style,
                    enable_emoji_overlays=add_emojis,
                    enable_importance_highlighting=use_ai_features
                )
                results['processed_videos']['with_ai_subtitles'] = subtitled_path
                current_video = subtitled_path
            else:
                current_video = video_path

            # Step 5: Add clipart overlays (optional)
            if add_clipart and clipart_urls:
                logger.info("Adding clipart overlays...")
                key_nouns = self.extract_key_nouns(transcript_data)
                clipart_path = os.path.join(output_dir, "video_with_clipart.mp4")
                self.add_clipart_overlays(current_video, key_nouns, clipart_urls, clipart_path)
                results['processed_videos']['with_clipart'] = clipart_path
                current_video = clipart_path

            # Step 6: Create short-form content (optional)
            if create_short_form:
                logger.info("Creating short-form content...")
                try:
                    for platform in platforms:
                        logger.info(f"Creating {platform} content...")
                        short_clips = self.create_short_form_content(current_video, platform, max_short_clips)
                        if short_clips:
                            results['short_form_clips'][platform] = short_clips
                        else:
                            logger.warning(f"No short clips created for platform: {platform}")
                except Exception as e:
                    logger.error(f"Error in short-form content creation: {e}")

            # Calculate processing time
            end_time = datetime.now()
            results['processing_time'] = (end_time - start_time).total_seconds()

            logger.info(f"AI video processing completed in {results['processing_time']:.2f} seconds")

            return results

        except Exception as e:
            logger.error(f"Error in comprehensive AI video processing: {e}")
            results['error'] = str(e)
            return results


# Utility functions for external use
def process_video_with_enhancements(video_path: str, output_dir: str,
                                     openai_api_key: str = None,
                                     options: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Convenience function to process a video with all AI enhancements
    """
    processor = AdvancedVideoProcessor(openai_api_key=openai_api_key)
    return processor.process_video_comprehensive(video_path, output_dir, options)


def create_quick_ai_tiktok_clip(video_path: str, output_path: str,
                               openai_api_key: str = None) -> str:
    """
    Quick function to create a TikTok-ready clip with AI-powered features
    """
    processor = AdvancedVideoProcessor(openai_api_key=openai_api_key)

    # Extract transcript
    transcript_data = processor.extract_transcript_with_timing(video_path)

    # AI analysis for emojis and importance
    if openai_api_key:
        transcript_data = processor.analyze_content_with_ai(transcript_data)
    else:
        transcript_data = processor.analyze_emotions_and_add_emojis(transcript_data)

    # Create AI-enhanced video
    processor.create_ai_enhanced_subtitles(
        video_path,
        transcript_data,
        output_path,
        'clean_modern',
        enable_emoji_overlays=True,
        enable_importance_highlighting=True
    )

    return output_path


def create_video_with_emoji_overlays_only(video_path: str, output_path: str,
                                        openai_api_key: str = None,
                                        style: str = "clean_modern") -> str:
    """
    Create a video with only emoji overlays (no importance highlighting)
    """
    processor = AdvancedVideoProcessor(openai_api_key=openai_api_key)

    # Extract transcript
    transcript_data = processor.extract_transcript_with_timing(video_path)

    # Add emojis
    if openai_api_key:
        transcript_data = processor.analyze_content_with_ai(transcript_data)
    else:
        transcript_data = processor.analyze_emotions_and_add_emojis(transcript_data)

    # Create video with only emoji overlays
    processor.create_ai_enhanced_subtitles(
        video_path,
        transcript_data,
        output_path,
        style,
        enable_emoji_overlays=True,
        enable_importance_highlighting=False

    )

    return output_path


def create_video_with_importance_highlighting_only(video_path: str, output_path: str,
                                                 openai_api_key: str = None,
                                                 style: str = "clean_modern") -> str:
    """
    Create a video with only importance highlighting (no emoji overlays)
    """
    processor = AdvancedVideoProcessor(openai_api_key=openai_api_key)

    # Extract transcript
    transcript_data = processor.extract_transcript_with_timing(video_path)

    # AI analysis for importance detection
    if openai_api_key:
        transcript_data = processor.analyze_content_with_ai(transcript_data)

    # Create video with only importance highlighting
    processor.create_ai_enhanced_subtitles(
        video_path,
        transcript_data,
        output_path,
        style,
        enable_emoji_overlays=False,
        enable_importance_highlighting=True
    )

    return output_path


def analyze_video_importance(video_path: str, openai_api_key: str = None) -> Dict[str, Any]:
    """
    Analyze a video to detect important segments that would be highlighted
    """
    processor = AdvancedVideoProcessor(openai_api_key=openai_api_key)

    # Extract transcript
    transcript_data = processor.extract_transcript_with_timing(video_path)

    # AI analysis
    if openai_api_key:
        transcript_data = processor.analyze_content_with_ai(transcript_data)

    # Detect important segments
    important_segments = processor.detect_important_text_segments(transcript_data)

    return {
        'total_words': len(transcript_data),
        'important_segments': important_segments,
        'importance_count': len(important_segments),
        'ai_features_used': bool(openai_api_key),
        'video_duration': transcript_data[-1]['end'] if transcript_data else 0
    }
