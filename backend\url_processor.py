"""
URL Video Processing Module
Handles downloading and processing videos from URLs (YouTube, social media)
"""

import os
import re
import logging
import subprocess
from typing import Dict, List, Optional, Tuple
from urllib.parse import urlparse, parse_qs
import requests
from moviepy.editor import VideoFileClip

logger = logging.getLogger(__name__)

class URLVideoProcessor:
    """Handles video download and processing from various platforms"""
    
    def __init__(self, temp_dir: str = "temp"):
        self.temp_dir = temp_dir
        os.makedirs(temp_dir, exist_ok=True)
        
        # Platform patterns for URL detection
        self.platform_patterns = {
            'youtube': [
                r'(?:youtube\.com/watch\?v=|youtu\.be/|youtube\.com/embed/)([a-zA-Z0-9_-]+)',
                r'youtube\.com/shorts/([a-zA-Z0-9_-]+)'
            ],
            'tiktok': [
                r'tiktok\.com/@[\w.-]+/video/(\d+)',
                r'vm\.tiktok\.com/([a-zA-Z0-9]+)'
            ],
            'instagram': [
                r'instagram\.com/p/([a-zA-Z0-9_-]+)',
                r'instagram\.com/reel/([a-zA-Z0-9_-]+)'
            ],
            'twitter': [
                r'twitter\.com/\w+/status/(\d+)',
                r'x\.com/\w+/status/(\d+)'
            ],
            'facebook': [
                r'facebook\.com/watch/?\?v=(\d+)',
                r'fb\.watch/([a-zA-Z0-9]+)'
            ]
        }
    
    def detect_platform(self, url: str) -> Optional[str]:
        """Detect the platform from URL"""
        for platform, patterns in self.platform_patterns.items():
            for pattern in patterns:
                if re.search(pattern, url, re.IGNORECASE):
                    return platform
        return None
    
    def extract_video_id(self, url: str, platform: str) -> Optional[str]:
        """Extract video ID from URL"""
        patterns = self.platform_patterns.get(platform, [])
        for pattern in patterns:
            match = re.search(pattern, url, re.IGNORECASE)
            if match:
                return match.group(1)
        return None
    
    def get_video_metadata(self, url: str) -> Dict:
        """Get video metadata without downloading"""
        try:
            # Use yt-dlp to get metadata
            cmd = [
                'yt-dlp',
                '--dump-json',
                '--no-download',
                url
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                import json
                metadata = json.loads(result.stdout)
                return {
                    'title': metadata.get('title', 'Unknown'),
                    'duration': metadata.get('duration', 0),
                    'uploader': metadata.get('uploader', 'Unknown'),
                    'view_count': metadata.get('view_count', 0),
                    'upload_date': metadata.get('upload_date', ''),
                    'description': metadata.get('description', ''),
                    'thumbnail': metadata.get('thumbnail', ''),
                    'formats': len(metadata.get('formats', [])),
                    'platform': self.detect_platform(url)
                }
            else:
                logger.error(f"yt-dlp metadata extraction failed: {result.stderr}")
                return {'error': 'Failed to extract metadata'}
                
        except subprocess.TimeoutExpired:
            logger.error("Metadata extraction timed out")
            return {'error': 'Metadata extraction timed out'}
        except Exception as e:
            logger.error(f"Error extracting metadata: {str(e)}")
            return {'error': str(e)}
    
    def download_video(self, url: str, quality: str = 'best') -> Tuple[str, Dict]:
        """Download video from URL and return local path and metadata"""
        try:
            platform = self.detect_platform(url)
            if not platform:
                raise ValueError("Unsupported platform or invalid URL")
            
            video_id = self.extract_video_id(url, platform)
            if not video_id:
                raise ValueError("Could not extract video ID from URL")
            
            # Define output filename
            output_filename = f"{platform}_{video_id}.%(ext)s"
            output_path = os.path.join(self.temp_dir, output_filename)
            
            # Quality settings based on platform
            quality_settings = {
                'youtube': 'best[height<=1080]',
                'tiktok': 'best',
                'instagram': 'best',
                'twitter': 'best',
                'facebook': 'best[height<=720]'
            }
            
            selected_quality = quality_settings.get(platform, 'best')
            
            # Download command
            cmd = [
                'yt-dlp',
                '-f', selected_quality,
                '-o', output_path,
                '--no-playlist',
                '--extract-flat', 'false',
                url
            ]
            
            logger.info(f"Downloading video from {platform}: {url}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode != 0:
                logger.error(f"Download failed: {result.stderr}")
                raise Exception(f"Download failed: {result.stderr}")
            
            # Find the actual downloaded file
            downloaded_files = [f for f in os.listdir(self.temp_dir) 
                             if f.startswith(f"{platform}_{video_id}")]
            
            if not downloaded_files:
                raise Exception("Downloaded file not found")
            
            actual_path = os.path.join(self.temp_dir, downloaded_files[0])
            
            # Get video info
            try:
                clip = VideoFileClip(actual_path)
                metadata = {
                    'platform': platform,
                    'video_id': video_id,
                    'duration': clip.duration,
                    'resolution': f"{clip.size[0]}x{clip.size[1]}",
                    'fps': clip.fps,
                    'file_size': os.path.getsize(actual_path),
                    'local_path': actual_path
                }
                clip.close()
            except Exception as e:
                logger.error(f"Error getting video info: {str(e)}")
                metadata = {
                    'platform': platform,
                    'video_id': video_id,
                    'local_path': actual_path,
                    'error': str(e)
                }
            
            logger.info(f"Successfully downloaded video: {actual_path}")
            return actual_path, metadata
            
        except subprocess.TimeoutExpired:
            logger.error("Video download timed out")
            raise Exception("Video download timed out (5 minutes)")
        except Exception as e:
            logger.error(f"Error downloading video: {str(e)}")
            raise
    
    def validate_url(self, url: str) -> Dict:
        """Validate URL and return platform info"""
        try:
            # Basic URL validation
            parsed = urlparse(url)
            if not parsed.scheme or not parsed.netloc:
                return {'valid': False, 'error': 'Invalid URL format'}
            
            # Platform detection
            platform = self.detect_platform(url)
            if not platform:
                return {'valid': False, 'error': 'Unsupported platform'}
            
            # Check if URL is accessible
            try:
                response = requests.head(url, timeout=10)
                if response.status_code >= 400:
                    return {'valid': False, 'error': f'URL not accessible (HTTP {response.status_code})'}
            except requests.RequestException:
                # Some platforms block HEAD requests, so we'll allow this to pass
                pass
            
            return {
                'valid': True,
                'platform': platform,
                'video_id': self.extract_video_id(url, platform)
            }
            
        except Exception as e:
            return {'valid': False, 'error': str(e)}
    
    def cleanup_downloaded_file(self, file_path: str):
        """Clean up downloaded video file"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"Cleaned up file: {file_path}")
        except Exception as e:
            logger.error(f"Error cleaning up file {file_path}: {str(e)}")

# Utility functions for integration
def is_supported_url(url: str) -> bool:
    """Check if URL is from a supported platform"""
    processor = URLVideoProcessor()
    validation = processor.validate_url(url)
    return validation['valid']

def get_platform_from_url(url: str) -> Optional[str]:
    """Get platform name from URL"""
    processor = URLVideoProcessor()
    return processor.detect_platform(url)

def download_and_process_url(url: str, temp_dir: str = "temp") -> Tuple[str, Dict]:
    """Download video from URL and return path and metadata"""
    processor = URLVideoProcessor(temp_dir)
    return processor.download_video(url)
