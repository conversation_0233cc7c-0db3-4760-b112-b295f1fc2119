#!/usr/bin/env python3
"""
Test script for Advanced Video Processor
Demonstrates the functionality of subtitle generation, emoji addition, clipart overlays, and short-form content creation
"""

import os
import sys
import tempfile
from pathlib import Path

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from advanced_video_processor import AdvancedVideoProcessor, process_video_with_enhancements

def test_advanced_processing():
    """
    Test the advanced video processing functionality
    """
    print("🎬 Testing Advanced Video Processor")
    print("=" * 50)
    
    # Test video path (you'll need to provide a real video file)
    test_video_path = "test_video.mp4"  # Replace with actual video path
    
    if not os.path.exists(test_video_path):
        print(f"❌ Test video not found: {test_video_path}")
        print("Please provide a test video file and update the path in this script.")
        return
    
    # Create output directory
    output_dir = tempfile.mkdtemp(prefix="advanced_video_test_")
    print(f"📁 Output directory: {output_dir}")
    
    try:
        # Initialize processor
        processor = AdvancedVideoProcessor()
        
        # Test 1: Extract transcript with timing
        print("\n🎤 Testing transcript extraction...")
        transcript_data = processor.extract_transcript_with_timing(test_video_path)
        print(f"✅ Extracted {len(transcript_data)} words with timing")
        
        if transcript_data:
            print("Sample transcript data:")
            for i, word_data in enumerate(transcript_data[:5]):
                print(f"  {i+1}. '{word_data['word']}' ({word_data['start']:.2f}s - {word_data['end']:.2f}s)")
        
        # Test 2: Emotion analysis and emoji addition
        print("\n😊 Testing emotion analysis and emoji addition...")
        enhanced_transcript = processor.analyze_emotions_and_add_emojis(transcript_data)
        emoji_count = sum(1 for word in enhanced_transcript if word.get('emoji'))
        print(f"✅ Added {emoji_count} emojis based on emotional analysis")
        
        # Test 3: Key noun extraction
        print("\n🔍 Testing key noun extraction...")
        key_nouns = processor.extract_key_nouns(transcript_data)
        print(f"✅ Extracted {len(key_nouns)} key nouns")
        
        if key_nouns:
            print("Key nouns found:")
            for noun_data in key_nouns[:5]:
                print(f"  - '{noun_data['noun']}' at {noun_data['start']:.2f}s")
        
        # Test 4: Clipart search
        print("\n🖼️ Testing clipart search...")
        clipart_urls = processor.search_clipart_images(key_nouns)
        print(f"✅ Found clipart for {len(clipart_urls)} nouns")
        
        # Test 5: Create animated subtitles
        print("\n📝 Testing animated subtitle creation...")
        subtitle_output = os.path.join(output_dir, "video_with_subtitles.mp4")
        try:
            processor.create_animated_subtitles(
                test_video_path, 
                enhanced_transcript, 
                subtitle_output, 
                style="modern"
            )
            print(f"✅ Created subtitled video: {subtitle_output}")
        except Exception as e:
            print(f"⚠️ Subtitle creation failed: {e}")
        
        # Test 6: Add clipart overlays
        if clipart_urls and os.path.exists(subtitle_output):
            print("\n🎨 Testing clipart overlay addition...")
            clipart_output = os.path.join(output_dir, "video_with_clipart.mp4")
            try:
                processor.add_clipart_overlays(
                    subtitle_output, 
                    key_nouns, 
                    clipart_urls, 
                    clipart_output
                )
                print(f"✅ Added clipart overlays: {clipart_output}")
            except Exception as e:
                print(f"⚠️ Clipart overlay failed: {e}")
        
        # Test 7: Create short-form content
        print("\n📱 Testing short-form content creation...")
        try:
            short_clips = processor.create_short_form_content(
                test_video_path, 
                platform='tiktok', 
                max_clips=2
            )
            print(f"✅ Created {len(short_clips)} TikTok-ready clips")
            for i, clip_path in enumerate(short_clips):
                print(f"  Clip {i+1}: {clip_path}")
        except Exception as e:
            print(f"⚠️ Short-form creation failed: {e}")
        
        # Test 8: Comprehensive processing
        print("\n🚀 Testing comprehensive processing...")
        options = {
            'add_subtitles': True,
            'add_emojis': True,
            'add_clipart': True,
            'create_short_form': True,
            'platforms': ['tiktok', 'instagram'],
            'subtitle_style': 'tiktok',
            'max_short_clips': 2
        }
        
        try:
            results = processor.process_video_comprehensive(
                test_video_path, 
                output_dir, 
                options
            )
            
            print("✅ Comprehensive processing completed!")
            print(f"Processing time: {results['processing_time']:.2f} seconds")
            print(f"Processed videos: {list(results['processed_videos'].keys())}")
            print(f"Short-form clips: {list(results['short_form_clips'].keys())}")
            
        except Exception as e:
            print(f"⚠️ Comprehensive processing failed: {e}")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print(f"\n📁 Test files saved in: {output_dir}")
        print("🧹 Remember to clean up test files when done!")

def test_convenience_functions():
    """
    Test the convenience functions
    """
    print("\n🛠️ Testing Convenience Functions")
    print("=" * 50)
    
    test_video_path = "test_video.mp4"  # Replace with actual video path
    
    if not os.path.exists(test_video_path):
        print(f"❌ Test video not found: {test_video_path}")
        return
    
    # Test quick TikTok clip creation
    output_path = os.path.join(tempfile.gettempdir(), "quick_tiktok_clip.mp4")
    
    try:
        from advanced_video_processor import create_quick_tiktok_clip
        
        print("📱 Creating quick TikTok clip...")
        result_path = create_quick_tiktok_clip(test_video_path, output_path)
        print(f"✅ Quick TikTok clip created: {result_path}")
        
    except Exception as e:
        print(f"⚠️ Quick TikTok clip creation failed: {e}")

if __name__ == "__main__":
    print("🎬 Advanced Video Processor Test Suite")
    print("=" * 60)
    
    # Check if required dependencies are available
    try:
        import nltk
        import spacy
        import cv2
        import whisper_timestamped
        print("✅ All required dependencies are available")
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Please install required packages:")
        print("pip install nltk spacy opencv-python whisper-timestamped transformers torch")
        print("python -m spacy download en_core_web_sm")
        sys.exit(1)
    
    # Run tests
    test_advanced_processing()
    test_convenience_functions()
    
    print("\n🎉 Test suite completed!")
    print("Check the output files to see the results of video processing.")
