import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { LogIn, LogOut, User, UserPlus, AlertCircle, CheckCircle } from 'lucide-react';

const AuthDebug = () => {
  const { 
    user, 
    isAuthenticated, 
    isLoading, 
    signInWithGoogle, 
    register,
    login,
    logout,
    supabase,
    isSupabaseConfigured 
  } = useAuth();
  const { toast } = useToast();

  // Registration form state
  const [regUsername, setRegUsername] = useState('');
  const [regEmail, setRegEmail] = useState('');
  const [regPassword, setRegPassword] = useState('');
  const [regLoading, setRegLoading] = useState(false);

  // Login form state
  const [loginEmail, setLoginEmail] = useState('');
  const [loginPassword, setLoginPassword] = useState('');
  const [loginLoading, setLoginLoading] = useState(false);

  // Debug info state
  const [debugInfo, setDebugInfo] = useState<any>(null);

  const handleTestRegistration = async (e: React.FormEvent) => {
    e.preventDefault();
    setRegLoading(true);
    setDebugInfo(null);

    try {
      console.log('Starting registration with:', { regUsername, regEmail, regPassword: '***' });
      
      // Test Supabase connection first
      const { data: testData, error: testError } = await supabase.auth.getSession();
      console.log('Supabase connection test:', { testData, testError });

      await register(regUsername, regEmail, regPassword);
      
      toast({
        title: "Registration successful!",
        description: "Account created successfully",
      });
      
      setRegUsername('');
      setRegEmail('');
      setRegPassword('');
    } catch (error: any) {
      console.error('Registration failed:', error);
      setDebugInfo({
        error: error.message,
        code: error.code,
        status: error.status,
        details: error.details,
        hint: error.hint,
        full: error
      });
      
      toast({
        title: "Registration failed",
        description: error.message || "Unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setRegLoading(false);
    }
  };

  const handleTestLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoginLoading(true);

    try {
      await login(loginEmail, loginPassword);
      toast({
        title: "Login successful!",
        description: "Welcome back",
      });
      setLoginEmail('');
      setLoginPassword('');
    } catch (error: any) {
      console.error('Login failed:', error);
      toast({
        title: "Login failed",
        description: error.message || "Unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setLoginLoading(false);
    }
  };

  const handleGoogleAuth = async () => {
    try {
      console.log('Starting Google OAuth...');
      await signInWithGoogle();
    } catch (error: any) {
      console.error('Google OAuth failed:', error);
      toast({
        title: "Google OAuth failed",
        description: error.message || "Unknown error occurred",
        variant: "destructive",
      });
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      toast({
        title: "Logged out",
        description: "Successfully logged out",
      });
    } catch (error: any) {
      console.error('Logout failed:', error);
      toast({
        title: "Logout failed",
        description: error.message || "Unknown error occurred",
        variant: "destructive",
      });
    }
  };

  const testSupabaseConnection = async () => {
    try {
      const { data, error } = await supabase.auth.getSession();
      console.log('Supabase session test:', { data, error });
      
      const { data: userData, error: userError } = await supabase.auth.getUser();
      console.log('Supabase user test:', { userData, userError });
      
      toast({
        title: "Supabase connection test",
        description: "Check console for details",
      });
    } catch (error: any) {
      console.error('Supabase connection test failed:', error);
      toast({
        title: "Supabase connection failed",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-6xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-2">Authentication Debug Center</h1>
          <p className="text-muted-foreground">Debug and test authentication functionality</p>
        </div>

        {/* Current Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Current Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center gap-2">
                {isAuthenticated ? <CheckCircle className="h-4 w-4 text-green-500" /> : <AlertCircle className="h-4 w-4 text-red-500" />}
                <span>Authentication: {isAuthenticated ? 'Logged In' : 'Not Logged In'}</span>
              </div>
              <div className="flex items-center gap-2">
                {isSupabaseConfigured ? <CheckCircle className="h-4 w-4 text-green-500" /> : <AlertCircle className="h-4 w-4 text-red-500" />}
                <span>Supabase: {isSupabaseConfigured ? 'Configured' : 'Not Configured'}</span>
              </div>
              <div className="flex items-center gap-2">
                {import.meta.env.VITE_GOOGLE_CLIENT_ID ? <CheckCircle className="h-4 w-4 text-green-500" /> : <AlertCircle className="h-4 w-4 text-red-500" />}
                <span>Google OAuth: {import.meta.env.VITE_GOOGLE_CLIENT_ID ? 'Configured' : 'Not Configured'}</span>
              </div>
            </div>
            
            {user && (
              <div className="mt-4 p-3 bg-muted/50 rounded-lg">
                <h3 className="font-semibold mb-2">User Info:</h3>
                <pre className="text-xs">{JSON.stringify(user, null, 2)}</pre>
              </div>
            )}
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Registration Test */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <UserPlus className="h-5 w-5" />
                Test Registration
              </CardTitle>
              <CardDescription>Test manual registration functionality</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleTestRegistration} className="space-y-4">
                <Input
                  placeholder="Username"
                  value={regUsername}
                  onChange={(e) => setRegUsername(e.target.value)}
                  required
                />
                <Input
                  type="email"
                  placeholder="Email"
                  value={regEmail}
                  onChange={(e) => setRegEmail(e.target.value)}
                  required
                />
                <Input
                  type="password"
                  placeholder="Password"
                  value={regPassword}
                  onChange={(e) => setRegPassword(e.target.value)}
                  required
                />
                <Button type="submit" disabled={regLoading} className="w-full">
                  {regLoading ? 'Creating Account...' : 'Test Registration'}
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Login Test */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <LogIn className="h-5 w-5" />
                Test Login
              </CardTitle>
              <CardDescription>Test manual login functionality</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleTestLogin} className="space-y-4">
                <Input
                  type="email"
                  placeholder="Email"
                  value={loginEmail}
                  onChange={(e) => setLoginEmail(e.target.value)}
                  required
                />
                <Input
                  type="password"
                  placeholder="Password"
                  value={loginPassword}
                  onChange={(e) => setLoginPassword(e.target.value)}
                  required
                />
                <Button type="submit" disabled={loginLoading} className="w-full">
                  {loginLoading ? 'Signing In...' : 'Test Login'}
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>

        {/* OAuth and Actions */}
        <Card>
          <CardHeader>
            <CardTitle>OAuth & Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-4">
              <Button onClick={handleGoogleAuth} variant="outline">
                Sign in with Google
              </Button>
              <Button onClick={handleLogout} variant="outline">
                Logout
              </Button>
              <Button onClick={testSupabaseConnection} variant="secondary">
                Test Supabase Connection
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Debug Information */}
        {debugInfo && (
          <Card>
            <CardHeader>
              <CardTitle className="text-red-600">Debug Information</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="text-xs bg-muted p-4 rounded-lg overflow-auto">
                {JSON.stringify(debugInfo, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )}

        {/* Environment Info */}
        <Card>
          <CardHeader>
            <CardTitle>Environment Configuration</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Google Client ID:</span>
                <Badge variant={import.meta.env.VITE_GOOGLE_CLIENT_ID ? "default" : "destructive"}>
                  {import.meta.env.VITE_GOOGLE_CLIENT_ID ? 'Set' : 'Missing'}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Supabase URL:</span>
                <Badge variant={import.meta.env.VITE_SUPABASE_URL ? "default" : "destructive"}>
                  {import.meta.env.VITE_SUPABASE_URL ? 'Set' : 'Missing'}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Supabase Key:</span>
                <Badge variant={import.meta.env.VITE_SUPABASE_ANON_KEY ? "default" : "destructive"}>
                  {import.meta.env.VITE_SUPABASE_ANON_KEY ? 'Set' : 'Missing'}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AuthDebug;
