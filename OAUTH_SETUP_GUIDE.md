# 🔧 Google OAuth Setup Guide for SmartClips

## 🚨 Current Issues Identified:

1. **AuthRetryableFetchError (Status 0)**: Network/CORS issue with Supabase
2. **Google OAuth not configured**: Missing provider setup in Supabase
3. **Environment variables**: Some missing configurations

## 📋 Step-by-Step Fix Guide:

### 1. 🔑 Google Cloud Console Setup

1. **Go to**: [Google Cloud Console](https://console.cloud.google.com/)
2. **Select your project** or create a new one
3. **Navigate to**: APIs & Services → Credentials
4. **Find your OAuth 2.0 Client ID**: `************-39vld66dct9n4rqq1karu6s14as56qi5.apps.googleusercontent.com`

**Configure Authorized Origins:**
```
http://localhost:8080
https://ilwlliquljbgikgybbfm.supabase.co
```

**Configure Authorized Redirect URIs:**
```
http://localhost:8080
http://localhost:8080/auth-debug
https://ilwlliquljbgikgybbfm.supabase.co/auth/v1/callback
```

### 2. 🗄️ Supabase Dashboard Setup

1. **Go to**: [Supabase Dashboard](https://supabase.com/dashboard)
2. **Select your project**: `ilwlliquljbgikgybbfm`
3. **Navigate to**: Authentication → Providers
4. **Enable Google OAuth**:
   - Toggle ON the Google provider
   - **Client ID**: `************-39vld66dct9n4rqq1karu6s14as56qi5.apps.googleusercontent.com`
   - **Client Secret**: `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`
   - **Redirect URL**: `https://ilwlliquljbgikgybbfm.supabase.co/auth/v1/callback`

### 3. 📊 Database Schema Check

**Ensure the `profiles` table exists with these columns:**
```sql
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  first_name TEXT,
  last_name TEXT,
  avatar_url TEXT,
  credits INTEGER DEFAULT 10,
  role TEXT DEFAULT 'user',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles
  FOR INSERT WITH CHECK (auth.uid() = id);
```

### 4. 🔧 Environment Variables

**Frontend (.env):**
```env
VITE_SUPABASE_URL=https://ilwlliquljbgikgybbfm.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlsd2xsaXF1bGpiZ2lrZ3liYmZtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM2NjU4NjIsImV4cCI6MjA1OTI0MTg2Mn0.O1pEpoXG0UoO3RmxIy0QD78aZ4UVf-oViJk5ce4NNLI
VITE_GOOGLE_CLIENT_ID=************-39vld66dct9n4rqq1karu6s14as56qi5.apps.googleusercontent.com
VITE_API_URL=http://localhost:8000
VITE_CLOUDINARY_CLOUD_NAME=dang2mmzg
VITE_CLOUDINARY_API_KEY=246516753571446
```

**Backend (backend/.env):**
```env
DATABASE_URL=sqlite:///./quikclips.db
SECRET_KEY=yJB5dRzho5RlobJjfOKAiTDeztmLKVriYis6DTS9Svw
CLOUDINARY_CLOUD_NAME=dang2mmzg
CLOUDINARY_API_KEY=246516753571446
CLOUDINARY_API_SECRET=W_QkP6XKXcvT1HTqQuulqUCYYRE
OPENAI_API_KEY=********************************************************************************************************************************************************************
ELEVENLABS_API_KEY=***************************************************
```

## 🧪 Testing Steps:

### 1. Test Supabase Connection
```
Visit: http://localhost:8080/supabase-debug
Click: "Run Connection Tests"
```

### 2. Test Manual Registration
```
Visit: http://localhost:8080/auth-debug
Fill in registration form
Click: "Test Registration"
```

### 3. Test Google OAuth
```
Visit: http://localhost:8080/auth-debug
Click: "Sign in with Google"
Complete Google authorization
```

## 🔍 Debugging Tools Available:

1. **Supabase Debug**: `http://localhost:8080/supabase-debug`
   - Tests Supabase connection
   - Tests database queries
   - Tests auth provider configuration

2. **Auth Debug**: `http://localhost:8080/auth-debug`
   - Tests registration/login
   - Shows detailed error information
   - Tests OAuth flows

3. **Environment Check**: `http://localhost:8080/env-check`
   - Verifies all environment variables

## 🚨 Common Error Solutions:

### AuthRetryableFetchError (Status 0)
- **Cause**: Network/CORS issue or invalid Supabase configuration
- **Solution**: 
  1. Verify Supabase URL and key are correct
  2. Check if Supabase project is active
  3. Ensure no firewall blocking requests

### "Failed to fetch"
- **Cause**: Network connectivity or invalid endpoint
- **Solution**:
  1. Test Supabase connection manually
  2. Check browser network tab for failed requests
  3. Verify Supabase project status

### Google OAuth "Invalid provider"
- **Cause**: Google OAuth not enabled in Supabase
- **Solution**: Enable Google provider in Supabase dashboard

### Profile creation fails
- **Cause**: Missing profiles table or incorrect permissions
- **Solution**: Create profiles table with proper RLS policies

## 📞 Next Steps:

1. **Configure Supabase** with Google OAuth credentials
2. **Test connection** using the debug tools
3. **Create profiles table** if it doesn't exist
4. **Test authentication flow** end-to-end

After completing these steps, the authentication should work correctly!
