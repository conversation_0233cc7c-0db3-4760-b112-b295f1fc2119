# OAuth Configuration Fix

## 1. Supabase Dashboard Configuration (CRITICAL)

**Location**: https://supabase.com/dashboard/project/ilwlliquljbgikgybbfm
**Path**: Authentication → Providers → Google

**Required Settings**:
```
✅ Enable Google Provider: ON
✅ Client ID: 493964876159-39vld66dct9n4rqq1karu6s14as56qi5.apps.googleusercontent.com
✅ Client Secret: 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
✅ Redirect URL: https://ilwlliquljbgikgybbfm.supabase.co/auth/v1/callback
```

## 2. Google Cloud Console Configuration

**Location**: https://console.cloud.google.com/apis/credentials
**OAuth 2.0 Client ID**: 493964876159-39vld66dct9n4rqq1karu6s14as56qi5.apps.googleusercontent.com

**Authorized JavaScript Origins**:
```
http://localhost:8080
https://ilwlliquljbgikgybbfm.supabase.co
```

**Authorized Redirect URIs**:
```
http://localhost:8080
http://localhost:8080/auth-debug
https://ilwlliquljbgikgybbfm.supabase.co/auth/v1/callback
```

## 3. Code Fix for OAuth Error Handling

**File**: `src/context/AuthContext.tsx`
**Lines**: 504-553

The OAuth function has been updated with better error handling. Test with:
```bash
# Visit this URL to test OAuth
http://localhost:8080/supabase-debug
```

## 4. Environment Variables Check

**File**: `.env` (root directory)
```env
VITE_SUPABASE_URL=https://ilwlliquljbgikgybbfm.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.O1pEpoXG0UoO3RmxIy0QD78aZ4UVf-oViJk5ce4NNLI
VITE_GOOGLE_CLIENT_ID=493964876159-39vld66dct9n4rqq1karu6s14as56qi5.apps.googleusercontent.com
```

## 5. Testing Commands

```bash
# Test OAuth configuration
curl -I "https://ilwlliquljbgikgybbfm.supabase.co/auth/v1/authorize?provider=google"

# Test Supabase connection
curl -H "apikey: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.O1pEpoXG0UoO3RmxIy0QD78aZ4UVf-oViJk5ce4NNLI" \
"https://ilwlliquljbgikgybbfm.supabase.co/rest/v1/profiles?select=count"
```
