import os
import tempfile
import logging
import time
from typing import List, Dict, Any, <PERSON><PERSON>
import speech_recognition as sr
from moviepy.editor import VideoFileClip, concatenate_videoclips, AudioFileClip
import openai
from pydub import AudioSegment

logger = logging.getLogger(__name__)


def transcribe_video(video_path: str, temp_dir: str) -> Tuple[str, List[Dict[str, float]]]:
    """Transcribe video and return transcript with timestamps"""
    video = None
    audio_path = None

    try:
        # Extract audio from video
        video = VideoFileClip(video_path)

        audio_path = os.path.join(temp_dir, "audio_extract.wav")

        # Extract audio with better settings using pydub
        video.audio.write_audiofile(
            audio_path,
            codec='pcm_s16le',
            ffmpeg_params=["-ac", "1", "-ar", "16000"],  # Mono, 16kHz
            verbose=False
        )

        # Process audio with pydub
        audio = AudioSegment.from_wav(audio_path)

        # Split audio into chunks of 30 seconds
        chunk_length_ms = 30000  # 30 seconds
        chunks = [audio[i:i + chunk_length_ms]
                  for i in range(0, len(audio), chunk_length_ms)]

        transcript = ""
        timestamps = []
        current_time = 0

        # Initialize recognizer with optimized settings
        r = sr.Recognizer()
        r.energy_threshold = 300
        r.dynamic_energy_threshold = True
        r.pause_threshold = 0.8

        # Process each chunk
        for i, chunk in enumerate(chunks):
            # Export chunk to temporary file
            chunk_path = os.path.join(temp_dir, f"chunk_{i}.wav")
            chunk.export(chunk_path, format="wav")
            try:
                with sr.AudioFile(chunk_path) as source:
                    # Adjust for ambient noise
                    r.adjust_for_ambient_noise(source, duration=0.5)
                    audio_data = r.record(source)

                    # Try to recognize the chunk
                    try:
                        result = r.recognize_google(
                            audio_data,
                            language="en-US",
                            show_all=True
                        )

                        if result and 'alternative' in result and result['alternative']:
                            chunk_transcript = result['alternative'][0]['transcript']

                            # Calculate timestamps for this chunk
                            # Convert ms to seconds
                            chunk_duration = len(chunk) / 1000.0
                            words = chunk_transcript.split()
                            time_per_word = chunk_duration / \
                                len(words) if words else 0

                            for word in words:
                                timestamps.append({
                                    "word": word,
                                    "start": round(current_time, 2),
                                    "end": round(current_time + time_per_word, 2)
                                })
                                current_time += time_per_word

                            if transcript:
                                transcript += " " + chunk_transcript
                            else:
                                transcript = chunk_transcript

                    except sr.UnknownValueError:
                        logger.warning(
                            f"Could not understand audio in chunk {i}")
                    except sr.RequestError as e:
                        logger.error(f"Error in chunk {i}: {str(e)}")

            finally:
                # Clean up chunk file
                if os.path.exists(chunk_path):
                    os.remove(chunk_path)

        return transcript, timestamps

    except Exception as e:
        logger.error(f"Error in transcribe_video: {str(e)}")
        return "", []
    finally:
        # Clean up audio file
        if audio_path and os.path.exists(audio_path):
            try:
                os.remove(audio_path)
            except Exception as e:
                logger.error(f"Error removing audio file: {str(e)}")

        # Ensure video is closed
        if video is not None:
            try:
                video.reader.close()
                if video.audio:
                    video.audio.reader.close_proc()
                del video
            except Exception as e:
                logger.error(f"Error cleaning up video object: {str(e)}")


def segment_transcript(
    transcript: str,
    timestamps: List[Dict[str, float]],
    min_duration: float = 10.0,
    max_duration: float = 60.0,
    refine_with_ai: bool = False
) -> List[Dict[str, Any]]:
    """Segment transcript into coherent parts"""
    try:
        if not transcript or not timestamps:
            return []

        # Create segments based on max_duration
        segments = []
        current_segment = {
            "text": "",
            "start": timestamps[0]["start"] if timestamps else 0,
            "end": 0,
            "words": []
        }

        # Group words into segments based on duration
        for ts in timestamps:
            word = ts["word"]
            current_segment["words"].append(word)
            current_segment["end"] = ts["end"]

            # Check if we've reached max_duration
            segment_duration = current_segment["end"] - \
                current_segment["start"]
            if segment_duration >= max_duration:
                # Join words to form text
                current_segment["text"] = " ".join(current_segment["words"])
                segments.append({
                    "text": current_segment["text"],
                    "start": current_segment["start"],
                    "end": current_segment["end"]
                })

                # Start new segment
                current_segment = {
                    "text": "",
                    "start": ts["end"],
                    "end": ts["end"],
                    "words": []
                }

        # Add the last segment if it has content
        if current_segment["words"]:
            current_segment["text"] = " ".join(current_segment["words"])
            segments.append({
                "text": current_segment["text"],
                "start": current_segment["start"],
                "end": current_segment["end"]
            })

        # Filter out segments that are too short
        segments = [s for s in segments if (
            s["end"] - s["start"]) >= min_duration]

        # If enabled and OpenAI API key is available, refine segments with AI
        if refine_with_ai and os.getenv("OPENAI_API_KEY"):
            try:
                openai_client = openai.OpenAI(
                    api_key=os.getenv("OPENAI_API_KEY"))

                # Prepare the transcript for AI processing
                prompt = (
                    "The following is a video transcript. Please identify the most interesting "
                    "and coherent segments that would make good standalone clips:\n\n"
                    f"{transcript}\n\n"
                    "For each segment, provide the start and end sentences that mark the boundaries "
                    "of an interesting clip. List 3-5 segments in order of appearance."
                )

                response = openai_client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=[
                        {"role": "system", "content": "You are a video editor assistant that identifies the most interesting parts of a transcript."},
                        {"role": "user", "content": prompt}
                    ]
                )

                # Process AI response (simplified)
                ai_suggestions = response.choices[0].message.content
                logger.info(f"AI suggestions: {ai_suggestions}")

            except Exception as e:
                logger.error(f"Error refining with AI: {str(e)}")

        return segments
    except Exception as e:
        logger.error(f"Error in segment_transcript: {str(e)}")
        raise


def clip_video_from_text(
    video_path: str,
    segments: List[Dict[str, Any]],
    output_dir: str
) -> List[str]:
    """Clip video based on transcript segments"""
    try:
        output_paths = []

        for i, segment in enumerate(segments):
            # Ensure start and end times are within video duration
            start_time = max(0, segment["start"])
            end_time = min(segment["end"], segment["end"])

            # Skip invalid segments
            if end_time <= start_time:
                continue

            # Save clip with audio
            output_path = os.path.join(
                output_dir, f"clip_{i}_{os.path.basename(video_path)}")

            # Use ffmpeg directly to ensure proper audio handling
            import subprocess
            cmd = [
                'ffmpeg',
                '-i', video_path,
                '-ss', str(start_time),
                '-to', str(end_time),
                '-c:v', 'libx264',
                '-c:a', 'aac',
                '-strict', '-2',
                '-preset', 'ultrafast',
                '-threads', '4',
                '-ac', '2',
                '-ar', '44100',
                '-b:a', '192k',
                output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode != 0:
                logger.error(f"Error creating clip {i}: {result.stderr}")
                continue

            # Verify the output file
            if not os.path.exists(output_path) or os.path.getsize(output_path) == 0:
                logger.error(
                    f"Output file {output_path} is empty or does not exist")
                continue

            # Check video and audio streams
            probe_result = subprocess.run([
                'ffprobe',
                '-v', 'error',
                '-show_entries', 'stream=codec_type,codec_name',
                '-of', 'json',
                output_path
            ], capture_output=True, text=True)

            if 'audio' not in probe_result.stdout:
                logger.error(f"Output clip {output_path} has no audio track")
                continue

            logger.info(f"Successfully created clip: {output_path}")
            output_paths.append(output_path)

        return output_paths
    except Exception as e:
        logger.error(f"Error in clip_video_from_text: {str(e)}")
        raise


def trim_video(input_path: str, output_path: str, start_time: float, end_time: float) -> str:
    """Trim video to specified start and end times"""
    video = None
    trimmed = None
    try:
        video = VideoFileClip(input_path)
        trimmed = video.subclip(start_time, end_time)
        trimmed.write_videofile(output_path)
        return output_path
    except Exception as e:
        logger.error(f"Error in trim_video: {str(e)}")
        raise
    finally:
        if trimmed is not None:
            try:
                del trimmed
            except Exception as e:
                logger.error(f"Error deleting trimmed video: {str(e)}")
        if video is not None:
            try:
                del video
            except Exception as e:
                logger.error(f"Error deleting video: {str(e)}")


def adjust_speed(input_path: str, output_path: str, speed_factor: float) -> str:
    """Adjust video speed"""
    video = None
    modified = None
    try:
        video = VideoFileClip(input_path)
        modified = video.fx(VideoFileClip.speedx, speed_factor)
        modified.write_videofile(output_path)
        return output_path
    except Exception as e:
        logger.error(f"Error in adjust_speed: {str(e)}")
        raise
    finally:
        if modified is not None:
            try:
                del modified
            except Exception as e:
                logger.error(f"Error deleting modified video: {str(e)}")
        if video is not None:
            try:
                del video
            except Exception as e:
                logger.error(f"Error deleting video: {str(e)}")


def crop_video(input_path: str, output_path: str, x1: int, y1: int, x2: int, y2: int) -> str:
    """Crop video to specified rectangle"""
    video = None
    cropped = None
    try:
        video = VideoFileClip(input_path)
        cropped = video.crop(x1=x1, y1=y1, x2=x2, y2=y2)
        cropped.write_videofile(output_path)
        return output_path
    except Exception as e:
        logger.error(f"Error in crop_video: {str(e)}")
        raise
    finally:
        if cropped is not None:
            try:
                del cropped
            except Exception as e:
                logger.error(f"Error deleting cropped video: {str(e)}")
        if video is not None:
            try:
                del video
            except Exception as e:
                logger.error(f"Error deleting video: {str(e)}")


def rotate_video(input_path: str, output_path: str, angle: int) -> str:
    """Rotate video by specified angle"""
    video = None
    rotated = None
    try:
        video = VideoFileClip(input_path)
        rotated = video.rotate(angle)
        rotated.write_videofile(output_path)
        return output_path
    except Exception as e:
        logger.error(f"Error in rotate_video: {str(e)}")
        raise
    finally:
        if rotated is not None:
            try:
                del rotated
            except Exception as e:
                logger.error(f"Error deleting rotated video: {str(e)}")
        if video is not None:
            try:
                del video
            except Exception as e:
                logger.error(f"Error deleting video: {str(e)}")


def merge_videos(input_paths: List[str], output_path: str) -> str:
    """Merge multiple videos"""
    clips = []
    final_clip = None
    try:
        clips = [VideoFileClip(path) for path in input_paths]
        final_clip = concatenate_videoclips(clips)
        final_clip.write_videofile(output_path)
        return output_path
    except Exception as e:
        logger.error(f"Error in merge_videos: {str(e)}")
        raise
    finally:
        if final_clip is not None:
            try:
                del final_clip
            except Exception as e:
                logger.error(f"Error deleting final clip: {str(e)}")
        for clip in clips:
            try:
                del clip
            except Exception as e:
                logger.error(f"Error deleting clip: {str(e)}")


def segment_video_uniformly(video_path: str, min_duration: int, max_duration: int) -> List[Dict]:
    """Create segments of fixed length if transcription fails"""
    video = None
    try:
        video = VideoFileClip(video_path)
        duration = video.duration
        # fallback clip length between limits
        segment_length = max(min_duration, min(30, max_duration))

        segments = []
        start = 0
        while start < duration:
            end = min(start + segment_length, duration)
            segments.append({
                "start": start,
                "end": end,
                "text": f"Clip from {start:.2f}s to {end:.2f}s"
            })
            start = end

        return segments
    except Exception as e:
        logger.error(f"Error in segment_video_uniformly: {str(e)}")
        raise
    finally:
        if video is not None:
            try:
                del video
            except Exception as e:
                logger.error(f"Error deleting video object: {str(e)}")
