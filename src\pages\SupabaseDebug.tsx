import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { AlertCircle, CheckCircle, Wifi, Database, Key, Globe } from 'lucide-react';

const SupabaseDebug = () => {
  const { supabase } = useAuth();
  const { toast } = useToast();
  const [testResults, setTestResults] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);

  const runConnectionTest = async () => {
    setIsLoading(true);
    const results: any = {};

    try {
      // Test 1: Basic Supabase connection
      console.log('Testing Supabase connection...');
      results.connection = { status: 'testing', message: 'Testing connection...' };
      setTestResults({ ...results });

      try {
        const { data, error } = await supabase.auth.getSession();
        if (error) {
          results.connection = { status: 'error', message: error.message, details: error };
        } else {
          results.connection = { status: 'success', message: 'Connection successful', data };
        }
      } catch (err: any) {
        results.connection = { status: 'error', message: err.message, details: err };
      }

      // Test 2: Database query test
      console.log('Testing database query...');
      results.database = { status: 'testing', message: 'Testing database query...' };
      setTestResults({ ...results });

      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('count(*)')
          .limit(1);
        
        if (error) {
          results.database = { status: 'error', message: error.message, details: error };
        } else {
          results.database = { status: 'success', message: 'Database query successful', data };
        }
      } catch (err: any) {
        results.database = { status: 'error', message: err.message, details: err };
      }

      // Test 3: Auth provider test
      console.log('Testing auth providers...');
      results.authProviders = { status: 'testing', message: 'Testing auth providers...' };
      setTestResults({ ...results });

      try {
        // Try to get auth settings (this will fail if not properly configured)
        const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/auth/v1/settings`, {
          headers: {
            'apikey': import.meta.env.VITE_SUPABASE_ANON_KEY,
            'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
          }
        });
        
        if (response.ok) {
          const settings = await response.json();
          results.authProviders = { 
            status: 'success', 
            message: 'Auth providers accessible', 
            data: settings 
          };
        } else {
          results.authProviders = { 
            status: 'error', 
            message: `HTTP ${response.status}: ${response.statusText}`,
            details: { status: response.status, statusText: response.statusText }
          };
        }
      } catch (err: any) {
        results.authProviders = { status: 'error', message: err.message, details: err };
      }

      // Test 4: Google OAuth URL test
      console.log('Testing Google OAuth URL...');
      results.googleOAuth = { status: 'testing', message: 'Testing Google OAuth URL...' };
      setTestResults({ ...results });

      try {
        const oauthUrl = `${import.meta.env.VITE_SUPABASE_URL}/auth/v1/authorize?provider=google&redirect_to=${encodeURIComponent(window.location.origin)}`;
        
        const response = await fetch(oauthUrl, { 
          method: 'HEAD',
          mode: 'no-cors' // This will prevent CORS errors for this test
        });
        
        results.googleOAuth = { 
          status: 'info', 
          message: 'OAuth URL constructed (CORS prevents full test)', 
          data: { url: oauthUrl }
        };
      } catch (err: any) {
        results.googleOAuth = { status: 'error', message: err.message, details: err };
      }

    } catch (error: any) {
      console.error('Test suite error:', error);
      toast({
        title: "Test failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setTestResults(results);
      setIsLoading(false);
    }
  };

  const testGoogleOAuth = async () => {
    try {
      console.log('Testing Google OAuth flow...');
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/auth-debug`,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          }
        }
      });

      if (error) {
        console.error('Google OAuth error:', error);
        toast({
          title: "Google OAuth failed",
          description: error.message,
          variant: "destructive",
        });
      }
    } catch (error: any) {
      console.error('Google OAuth test error:', error);
      toast({
        title: "Google OAuth test failed",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error': return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'testing': return <div className="h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />;
      case 'info': return <AlertCircle className="h-4 w-4 text-blue-500" />;
      default: return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success': return <Badge variant="default">Success</Badge>;
      case 'error': return <Badge variant="destructive">Error</Badge>;
      case 'testing': return <Badge variant="secondary">Testing...</Badge>;
      case 'info': return <Badge variant="outline">Info</Badge>;
      default: return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-6xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-2">Supabase Connection Debug</h1>
          <p className="text-muted-foreground">Diagnose Supabase connection and authentication issues</p>
        </div>

        {/* Environment Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Key className="h-5 w-5" />
              Environment Configuration
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span>Supabase URL:</span>
                  <Badge variant={import.meta.env.VITE_SUPABASE_URL ? "default" : "destructive"}>
                    {import.meta.env.VITE_SUPABASE_URL ? 'Set' : 'Missing'}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Supabase Key:</span>
                  <Badge variant={import.meta.env.VITE_SUPABASE_ANON_KEY ? "default" : "destructive"}>
                    {import.meta.env.VITE_SUPABASE_ANON_KEY ? 'Set' : 'Missing'}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Google Client ID:</span>
                  <Badge variant={import.meta.env.VITE_GOOGLE_CLIENT_ID ? "default" : "destructive"}>
                    {import.meta.env.VITE_GOOGLE_CLIENT_ID ? 'Set' : 'Missing'}
                  </Badge>
                </div>
              </div>
              <div className="text-xs space-y-1">
                <div><strong>URL:</strong> {import.meta.env.VITE_SUPABASE_URL || 'Not set'}</div>
                <div><strong>Key:</strong> {import.meta.env.VITE_SUPABASE_ANON_KEY ? `${import.meta.env.VITE_SUPABASE_ANON_KEY.substring(0, 20)}...` : 'Not set'}</div>
                <div><strong>Client ID:</strong> {import.meta.env.VITE_GOOGLE_CLIENT_ID || 'Not set'}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Test Controls */}
        <Card>
          <CardHeader>
            <CardTitle>Connection Tests</CardTitle>
            <CardDescription>Run comprehensive tests to diagnose connection issues</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <Button onClick={runConnectionTest} disabled={isLoading}>
                {isLoading ? 'Running Tests...' : 'Run Connection Tests'}
              </Button>
              <Button onClick={testGoogleOAuth} variant="outline">
                Test Google OAuth
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Test Results */}
        {Object.keys(testResults).length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(testResults).map(([testName, result]: [string, any]) => (
                  <div key={testName} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(result.status)}
                        <span className="font-medium capitalize">{testName.replace(/([A-Z])/g, ' $1')}</span>
                      </div>
                      {getStatusBadge(result.status)}
                    </div>
                    <div className="text-sm text-muted-foreground mb-2">{result.message}</div>
                    {result.details && (
                      <details className="text-xs">
                        <summary className="cursor-pointer text-blue-600">Show Details</summary>
                        <pre className="mt-2 p-2 bg-muted rounded overflow-auto">
                          {JSON.stringify(result.details, null, 2)}
                        </pre>
                      </details>
                    )}
                    {result.data && (
                      <details className="text-xs">
                        <summary className="cursor-pointer text-green-600">Show Data</summary>
                        <pre className="mt-2 p-2 bg-muted rounded overflow-auto">
                          {JSON.stringify(result.data, null, 2)}
                        </pre>
                      </details>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Troubleshooting Guide */}
        <Card>
          <CardHeader>
            <CardTitle>Troubleshooting Guide</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 text-sm">
              <div>
                <h3 className="font-semibold mb-2">Common Issues & Solutions:</h3>
                <ul className="space-y-2 list-disc list-inside">
                  <li><strong>AuthRetryableFetchError:</strong> Usually indicates network/CORS issues or invalid Supabase URL/key</li>
                  <li><strong>Failed to fetch:</strong> Check if Supabase URL is correct and accessible</li>
                  <li><strong>Google OAuth not working:</strong> Ensure Google OAuth is configured in Supabase dashboard</li>
                  <li><strong>Profile creation fails:</strong> Check if profiles table exists and has correct permissions</li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-semibold mb-2">Required Supabase Configuration:</h3>
                <ol className="space-y-1 list-decimal list-inside">
                  <li>Go to Supabase Dashboard → Authentication → Providers</li>
                  <li>Enable Google OAuth provider</li>
                  <li>Add Client ID: 493964876159-39vld66dct9n4rqq1karu6s14as56qi5.apps.googleusercontent.com</li>
                  <li>Add Client Secret: 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</li>
                  <li>Set redirect URL to: http://localhost:8080</li>
                </ol>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SupabaseDebug;
