import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import {
  Play,
  Pause,
  SkipBack,
  Ski<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Copy,
  Trash2,
  Plus,
  ZoomIn,
  ZoomOut
} from 'lucide-react';

interface TimelineClip {
  id: string;
  start: number;
  end: number;
  duration: number;
  url: string;
  title: string;
  track: number;
}

interface TimelineProps {
  clips: TimelineClip[];
  duration: number;
  currentTime: number;
  isPlaying: boolean;
  onTimeChange: (time: number) => void;
  onPlay: () => void;
  onPause: () => void;
  onClipUpdate: (clipId: string, updates: Partial<TimelineClip>) => void;
  onClipDelete: (clipId: string) => void;
  onClipAdd: (clip: Omit<TimelineClip, 'id'>) => void;
}

const Timeline: React.FC<TimelineProps> = ({
  clips,
  duration,
  currentTime,
  isPlaying,
  onTimeChange,
  onPlay,
  onPause,
  onClipUpdate,
  onClipDelete,
  onClipAdd
}) => {
  const [zoom, setZoom] = useState(1);
  const [selectedClip, setSelectedClip] = useState<string | null>(null);
  const [dragState, setDragState] = useState<{
    clipId: string;
    type: 'move' | 'resize-start' | 'resize-end';
    startX: number;
    startTime: number;
  } | null>(null);
  
  const timelineRef = useRef<HTMLDivElement>(null);
  const TRACK_HEIGHT = 60;
  const TIMELINE_HEIGHT = 200;
  const PIXELS_PER_SECOND = 50 * zoom;

  // Convert time to pixel position
  const timeToPixels = (time: number) => time * PIXELS_PER_SECOND;
  
  // Convert pixel position to time
  const pixelsToTime = (pixels: number) => pixels / PIXELS_PER_SECOND;

  // Format time for display
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Handle timeline click
  const handleTimelineClick = (e: React.MouseEvent) => {
    if (!timelineRef.current || dragState) return;
    
    const rect = timelineRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const time = pixelsToTime(x);
    onTimeChange(Math.max(0, Math.min(duration, time)));
  };

  // Handle clip mouse down
  const handleClipMouseDown = (e: React.MouseEvent, clipId: string, type: 'move' | 'resize-start' | 'resize-end') => {
    e.stopPropagation();
    const clip = clips.find(c => c.id === clipId);
    if (!clip) return;

    setDragState({
      clipId,
      type,
      startX: e.clientX,
      startTime: type === 'resize-end' ? clip.end : clip.start
    });
    setSelectedClip(clipId);
  };

  // Handle mouse move during drag
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!dragState || !timelineRef.current) return;

      const deltaX = e.clientX - dragState.startX;
      const deltaTime = pixelsToTime(deltaX);
      const clip = clips.find(c => c.id === dragState.clipId);
      if (!clip) return;

      let updates: Partial<TimelineClip> = {};

      switch (dragState.type) {
        case 'move':
          const newStart = Math.max(0, dragState.startTime + deltaTime);
          const newEnd = newStart + clip.duration;
          if (newEnd <= duration) {
            updates = { start: newStart, end: newEnd };
          }
          break;
        
        case 'resize-start':
          const newStartTime = Math.max(0, Math.min(clip.end - 0.1, dragState.startTime + deltaTime));
          updates = { 
            start: newStartTime, 
            duration: clip.end - newStartTime 
          };
          break;
        
        case 'resize-end':
          const newEndTime = Math.max(clip.start + 0.1, Math.min(duration, dragState.startTime + deltaTime));
          updates = { 
            end: newEndTime, 
            duration: newEndTime - clip.start 
          };
          break;
      }

      if (Object.keys(updates).length > 0) {
        onClipUpdate(dragState.clipId, updates);
      }
    };

    const handleMouseUp = () => {
      setDragState(null);
    };

    if (dragState) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [dragState, clips, duration, onClipUpdate]);

  // Render time markers
  const renderTimeMarkers = () => {
    const markers = [];
    const interval = zoom < 0.5 ? 10 : zoom < 1 ? 5 : 1;
    
    for (let time = 0; time <= duration; time += interval) {
      const x = timeToPixels(time);
      markers.push(
        <div
          key={time}
          className="absolute top-0 bottom-0 border-l border-gray-300"
          style={{ left: x }}
        >
          <span className="absolute -top-6 -left-4 text-xs text-gray-500">
            {formatTime(time)}
          </span>
        </div>
      );
    }
    
    return markers;
  };

  // Render clips
  const renderClips = () => {
    return clips.map((clip) => {
      const left = timeToPixels(clip.start);
      const width = timeToPixels(clip.duration);
      const top = clip.track * TRACK_HEIGHT + 30;
      const isSelected = selectedClip === clip.id;

      return (
        <div
          key={clip.id}
          className={`absolute bg-blue-500 rounded border-2 cursor-move select-none ${
            isSelected ? 'border-blue-300 shadow-lg' : 'border-blue-600'
          }`}
          style={{
            left,
            width,
            top,
            height: TRACK_HEIGHT - 10
          }}
          onMouseDown={(e) => handleClipMouseDown(e, clip.id, 'move')}
        >
          {/* Resize handles */}
          <div
            className="absolute left-0 top-0 bottom-0 w-2 bg-blue-300 cursor-ew-resize opacity-0 hover:opacity-100"
            onMouseDown={(e) => handleClipMouseDown(e, clip.id, 'resize-start')}
          />
          <div
            className="absolute right-0 top-0 bottom-0 w-2 bg-blue-300 cursor-ew-resize opacity-0 hover:opacity-100"
            onMouseDown={(e) => handleClipMouseDown(e, clip.id, 'resize-end')}
          />
          
          {/* Clip content */}
          <div className="p-2 text-white text-xs truncate">
            <div className="font-medium">{clip.title}</div>
            <div className="opacity-75">{formatTime(clip.duration)}</div>
          </div>
        </div>
      );
    });
  };

  // Render playhead
  const renderPlayhead = () => {
    const x = timeToPixels(currentTime);
    return (
      <div
        className="absolute top-0 bottom-0 w-0.5 bg-red-500 z-10 pointer-events-none"
        style={{ left: x }}
      >
        <div className="absolute -top-2 -left-2 w-4 h-4 bg-red-500 rounded-full" />
      </div>
    );
  };

  return (
    <div className="space-y-4">
      {/* Transport Controls */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={() => onTimeChange(Math.max(0, currentTime - 10))}
              >
                <SkipBack className="h-4 w-4" />
              </Button>
              
              <Button
                variant="outline"
                size="icon"
                onClick={isPlaying ? onPause : onPlay}
              >
                {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              </Button>
              
              <Button
                variant="outline"
                size="icon"
                onClick={() => onTimeChange(Math.min(duration, currentTime + 10))}
              >
                <SkipForward className="h-4 w-4" />
              </Button>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm font-mono">
                {formatTime(currentTime)} / {formatTime(duration)}
              </span>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={() => setZoom(Math.max(0.1, zoom - 0.2))}
              >
                <ZoomOut className="h-4 w-4" />
              </Button>
              
              <span className="text-sm w-12 text-center">{Math.round(zoom * 100)}%</span>
              
              <Button
                variant="outline"
                size="icon"
                onClick={() => setZoom(Math.min(3, zoom + 0.2))}
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Timeline */}
      <Card>
        <CardContent className="p-0">
          <div
            ref={timelineRef}
            className="relative bg-gray-50 overflow-x-auto cursor-crosshair"
            style={{ 
              height: TIMELINE_HEIGHT,
              width: Math.max(800, timeToPixels(duration) + 100)
            }}
            onClick={handleTimelineClick}
          >
            {/* Time markers */}
            {renderTimeMarkers()}
            
            {/* Track backgrounds */}
            {[0, 1, 2].map((track) => (
              <div
                key={track}
                className="absolute border-b border-gray-200"
                style={{
                  top: track * TRACK_HEIGHT + 30,
                  left: 0,
                  right: 0,
                  height: TRACK_HEIGHT
                }}
              />
            ))}
            
            {/* Clips */}
            {renderClips()}
            
            {/* Playhead */}
            {renderPlayhead()}
          </div>
        </CardContent>
      </Card>

      {/* Clip Controls */}
      {selectedClip && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="text-sm font-medium">
                Selected: {clips.find(c => c.id === selectedClip)?.title}
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <Copy className="h-4 w-4 mr-1" />
                  Duplicate
                </Button>
                <Button variant="outline" size="sm">
                  <Scissors className="h-4 w-4 mr-1" />
                  Split
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => {
                    onClipDelete(selectedClip);
                    setSelectedClip(null);
                  }}
                >
                  <Trash2 className="h-4 w-4 mr-1" />
                  Delete
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default Timeline;
