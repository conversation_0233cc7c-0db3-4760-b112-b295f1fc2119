
# A string used to distinguish different Supabase projects on the same host. Defaults to the project ref.
project_id = "ilwlliquljbgikgybbfm"

[api]
enabled = true
# Port to use for the API URL.
port = 54321
# Schemas to expose in the API. Tables, views and stored procedures in this schema will get API
# endpoints. public and storage are always included.
schemas = ["public", "storage", "graphql_public"]
# Extra schemas to add to the search_path of every request. public is always included.
extra_search_path = ["extensions"]
# The maximum number of rows returns from a view, table, or stored procedure. Limits payload size
# for accidental or malicious requests.
max_rows = 1000

[db]
# Port to use for the local database URL.
port = 54322
# Port used by DB studio and the API to connect to Postgres.
studio_port = 54323
# The database major version to use. This must be the same as your remote database's. Run `SHOW
# server_version;` on the remote database to check.
major_version = 15

[studio]
enabled = true
# Port to use for Supabase Studio.
port = 54324
# External URL of the API server that frontend connects to.
api_url = "http://localhost"

# Email testing server. Emails sent with the local dev setup are not actually sent - rather, they
# are monitored, and you can view the emails that would have been sent from the configured URL.
[inbucket]
enabled = true
# Port to use for the email testing server web interface.
port = 54325
# Uncomment to expose additional ports for testing user applications that send emails.
# smtp_port = 54326
# pop3_port = 54327

[storage]
enabled = true
# The maximum file size allowed (e.g. "5MB", "500KB").
file_size_limit = "50MiB"

[auth]
enabled = true
# The base URL of your website. Used as an allow-list for redirects and for constructing URLs used
# in emails.
site_url = "http://localhost:3000"
# A list of *exact* URLs that auth providers are permitted to redirect to post authentication.
additional_redirect_urls = [
  "https://localhost:3000"
]
# How long tokens are valid for, in seconds. Defaults to 3600 (1 hour), maximum 604,800 (1 week).
jwt_expiry = 3600
# If disabled, the refresh token will never expire.
enable_refresh_token_rotation = true
# Allows refresh tokens to be reused after expiry, up to the specified interval in seconds.
# Requires enable_refresh_token_rotation = true.
refresh_token_reuse_interval = 10
# Allow/disallow new user signups to your project.
enable_signup = true

[analytics]
enabled = false

[experimental]
# Configures Postgres storage engine to use OrioleDB (S3)
orioledb_version = ""

# Configure one of the supported SMS providers. Remove this section if you don't want SMS-based auth.
[auth.sms]
# Enable SMS-based auth
enabled = false

[auth.hooks]
# If you prefer to disable auth hooks regardless of environment variable settings.
enabled = true

[functions]
# the location to place functions
dir = "supabase/functions"

[functions.transcribe]
verify_jwt = true

[functions.generate-script]
verify_jwt = true

[functions.generate-video-script]
verify_jwt = true

[auth.external.google]
enabled = true
client_id = "your-client-id"
secret = "your-client-secret"
