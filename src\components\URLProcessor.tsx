import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Link, 
  Download, 
  Play, 
  Clock, 
  Eye, 
  User, 
  CheckCircle, 
  XCircle, 
  Loader2,
  Youtube,
  Music,
  Camera,
  MessageCircle
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import {
  validateURL,
  getVideoMetadata,
  processVideoURL,
  detectPlatform,
  isSupportedURL,
  formatDuration,
  formatViewCount,
  getPlatformQualityOptions,
  VideoMetadata
} from '@/services/urlProcessingService';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface URLProcessorProps {
  onProcessComplete?: (result: any) => void;
  onError?: (error: string) => void;
}

const URLProcessor: React.FC<URLProcessorProps> = ({ onProcessComplete, onError }) => {
  const [url, setUrl] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isLoadingMetadata, setIsLoadingMetadata] = useState(false);
  const [validationResult, setValidationResult] = useState<any>(null);
  const [metadata, setMetadata] = useState<VideoMetadata | null>(null);
  const [minDuration, setMinDuration] = useState(10);
  const [maxDuration, setMaxDuration] = useState(60);
  const [quality, setQuality] = useState('best');
  const { toast } = useToast();

  const platformIcons = {
    youtube: Youtube,
    tiktok: Music,
    instagram: Camera,
    twitter: MessageCircle,
    facebook: MessageCircle
  };

  const platformColors = {
    youtube: 'bg-red-100 text-red-800',
    tiktok: 'bg-black text-white',
    instagram: 'bg-pink-100 text-pink-800',
    twitter: 'bg-blue-100 text-blue-800',
    facebook: 'bg-blue-100 text-blue-800'
  };

  useEffect(() => {
    const delayedValidation = setTimeout(() => {
      if (url && url.length > 10) {
        handleValidateURL();
      } else {
        setValidationResult(null);
        setMetadata(null);
      }
    }, 500);

    return () => clearTimeout(delayedValidation);
  }, [url]);

  const handleValidateURL = async () => {
    if (!url) return;

    setIsValidating(true);
    try {
      const result = await validateURL(url);
      setValidationResult(result);

      if (result.valid) {
        // Load metadata if URL is valid
        setIsLoadingMetadata(true);
        try {
          const meta = await getVideoMetadata(url);
          setMetadata(meta);
        } catch (error) {
          console.error('Error loading metadata:', error);
          // Don't show error for metadata, as validation passed
        } finally {
          setIsLoadingMetadata(false);
        }
      }
    } catch (error) {
      console.error('Validation error:', error);
      setValidationResult({
        valid: false,
        error: 'Failed to validate URL'
      });
    } finally {
      setIsValidating(false);
    }
  };

  const handleProcessURL = async () => {
    if (!validationResult?.valid) {
      toast({
        title: 'Invalid URL',
        description: 'Please enter a valid video URL from a supported platform',
        variant: 'destructive'
      });
      return;
    }

    setIsProcessing(true);
    try {
      const result = await processVideoURL({
        url,
        min_duration: minDuration,
        max_duration: maxDuration,
        quality
      });

      toast({
        title: 'Processing Complete',
        description: `Generated ${result.video_urls.length} clips from the video`
      });

      onProcessComplete?.(result);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to process video';
      toast({
        title: 'Processing Failed',
        description: errorMessage,
        variant: 'destructive'
      });
      onError?.(errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  const getPlatformIcon = (platform: string) => {
    const IconComponent = platformIcons[platform as keyof typeof platformIcons] || Link;
    return <IconComponent className="h-4 w-4" />;
  };

  const getPlatformBadge = (platform: string) => {
    const colorClass = platformColors[platform as keyof typeof platformColors] || 'bg-gray-100 text-gray-800';
    return (
      <Badge className={colorClass}>
        {getPlatformIcon(platform)}
        <span className="ml-1 capitalize">{platform}</span>
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* URL Input */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Link className="h-5 w-5" />
            Process Video from URL
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Video URL</label>
            <div className="relative">
              <Input
                type="url"
                placeholder="Paste YouTube, TikTok, Instagram, or Twitter video URL..."
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                className="pr-10"
              />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                {isValidating ? (
                  <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                ) : validationResult?.valid ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : validationResult?.valid === false ? (
                  <XCircle className="h-4 w-4 text-red-500" />
                ) : null}
              </div>
            </div>
          </div>

          {/* Validation Result */}
          {validationResult && (
            <div className="space-y-2">
              {validationResult.valid ? (
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription className="flex items-center gap-2">
                    Valid URL detected
                    {validationResult.platform && getPlatformBadge(validationResult.platform)}
                  </AlertDescription>
                </Alert>
              ) : (
                <Alert variant="destructive">
                  <XCircle className="h-4 w-4" />
                  <AlertDescription>
                    {validationResult.error || 'Invalid or unsupported URL'}
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}

          {/* Video Metadata */}
          {metadata && (
            <Card className="border-dashed">
              <CardContent className="p-4">
                <div className="flex items-start gap-4">
                  {metadata.thumbnail && (
                    <img
                      src={metadata.thumbnail}
                      alt={metadata.title}
                      className="w-24 h-16 object-cover rounded"
                    />
                  )}
                  <div className="flex-1 space-y-2">
                    <h3 className="font-medium line-clamp-2">{metadata.title}</h3>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        {metadata.uploader}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {formatDuration(metadata.duration)}
                      </div>
                      <div className="flex items-center gap-1">
                        <Eye className="h-3 w-3" />
                        {formatViewCount(metadata.view_count)}
                      </div>
                    </div>
                    {metadata.platform && getPlatformBadge(metadata.platform)}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Processing Options */}
          {validationResult?.valid && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Min Duration (seconds)</label>
                <Input
                  type="number"
                  min="5"
                  max="300"
                  value={minDuration}
                  onChange={(e) => setMinDuration(Number(e.target.value))}
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Max Duration (seconds)</label>
                <Input
                  type="number"
                  min="10"
                  max="600"
                  value={maxDuration}
                  onChange={(e) => setMaxDuration(Number(e.target.value))}
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Quality</label>
                <Select value={quality} onValueChange={setQuality}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {validationResult.platform && 
                      getPlatformQualityOptions(validationResult.platform).map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))
                    }
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}

          {/* Process Button */}
          <Button
            onClick={handleProcessURL}
            disabled={!validationResult?.valid || isProcessing}
            className="w-full"
            size="lg"
          >
            {isProcessing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing Video...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Process & Create Clips
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Supported Platforms */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Supported Platforms</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {Object.keys(platformIcons).map((platform) => (
              <Badge key={platform} variant="outline" className="flex items-center gap-1">
                {getPlatformIcon(platform)}
                <span className="capitalize">{platform}</span>
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default URLProcessor;
