# 🚀 SmartClips Quick Reference Guide

## 📋 Essential Commands

### Development Setup
```bash
# Frontend
npm install                 # Install dependencies
npm run dev                 # Start development server (port 8080)
npm run build              # Build for production
npm run lint               # Run ESLint

# Backend
cd backend
pip install -r requirements.txt  # Install Python dependencies
uvicorn main:app --reload        # Start FastAPI server (port 8000)
python -m pytest                 # Run tests
```

### Docker Commands
```bash
docker-compose up -d       # Start all services
docker-compose down        # Stop all services
docker-compose logs        # View logs
docker-compose build      # Rebuild containers
```

## 🔧 Key File Locations

### Frontend Structure
```
src/
├── components/ui/         # Shadcn UI components
├── pages/                 # Main page components
├── services/             # API integration
├── context/              # Global state management
├── hooks/                # Custom React hooks
└── lib/                  # Utility functions
```

### Backend Structure
```
backend/
├── main.py              # FastAPI app & routes
├── models.py            # Database models
├── database.py          # DB configuration
├── video_processing.py  # Video AI processing
└── storage.py           # File storage management
```

## 🔑 Environment Variables

### Frontend (.env)
```bash
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_key
```

### Backend (.env)
```bash
SECRET_KEY=your_jwt_secret
DATABASE_URL=your_database_url
OPENAI_API_KEY=sk-proj-your_key
ELEVENLABS_API_KEY=sk_your_key
CLOUDINARY_CLOUD_NAME=your_cloud
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_secret
```

## 📱 Main Features & Files

### 🎬 Video Processing
- **Smart Clipper**: `src/pages/SmartClipper.tsx`
- **Video Creator**: `src/pages/VideoCreator.tsx`
- **Gallery**: `src/pages/Gallery.tsx`
- **Processing Service**: `src/services/videoProcessingService.ts`
- **Backend Processing**: `backend/video_processing.py`

### 🤖 AI Features
- **Avatar Creator**: `src/pages/AvatarCreator.tsx`
- **Script Generator**: `src/pages/ScriptGenerator.tsx`
- **Video Generator**: `src/pages/VideoGenerator.tsx`
- **OpenAI Service**: `src/services/openAiService.ts`
- **Backend AI**: `backend/main.py` (lines 545-850)

### 📊 Analytics & Dashboard
- **Analytics**: `src/pages/Analytics.tsx`
- **Homepage**: `src/pages/HomePage.tsx`
- **Dashboard Layout**: `src/components/Dashboard.tsx`

### 🔐 Authentication
- **Auth Context**: `src/context/AuthContext.tsx`
- **Login/Register**: `src/pages/Login.tsx`, `src/pages/Register.tsx`
- **Backend Auth**: `backend/main.py` (lines 84-200)

## 🎨 UI Components Quick Reference

### Common Components
```tsx
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
```

### Usage Examples
```tsx
// Button variants
<Button variant="default">Primary</Button>
<Button variant="outline">Secondary</Button>
<Button variant="ghost">Subtle</Button>

// Card structure
<Card>
  <CardHeader>
    <CardTitle>Title</CardTitle>
  </CardHeader>
  <CardContent>
    Content here
  </CardContent>
</Card>

// Tabs
<Tabs defaultValue="tab1">
  <TabsList>
    <TabsTrigger value="tab1">Tab 1</TabsTrigger>
    <TabsTrigger value="tab2">Tab 2</TabsTrigger>
  </TabsList>
  <TabsContent value="tab1">Content 1</TabsContent>
  <TabsContent value="tab2">Content 2</TabsContent>
</Tabs>
```

## 🔌 API Endpoints

### Authentication
- `POST /token` - Login and get JWT token
- `POST /register` - Create new user account
- `GET /users/me` - Get current user profile

### Video Processing
- `POST /upload` - Upload video file
- `POST /generate` - Generate AI script
- `POST /generate-audio` - Create audio from text
- `POST /generate-image` - Generate images
- `POST /generate-video` - Compile final video

### User Management
- `GET /users/{user_id}` - Get user details
- `PUT /users/{user_id}` - Update user profile
- `DELETE /users/{user_id}` - Delete user account

## 🎯 Common Development Tasks

### Adding a New Page
1. Create component in `src/pages/NewPage.tsx`
2. Add route in `src/App.tsx`
3. Add navigation link in `src/components/Dashboard.tsx`
4. Create any needed services in `src/services/`

### Adding a New API Endpoint
1. Add route function in `backend/main.py`
2. Create Pydantic models for request/response
3. Add database operations if needed
4. Create frontend service function
5. Integrate with React components

### Adding a New UI Component
1. Create component in `src/components/ui/`
2. Follow Shadcn UI patterns
3. Add TypeScript interfaces
4. Export from component index
5. Document usage examples

### Database Changes
1. Modify models in `backend/models.py`
2. Create migration with Alembic
3. Update TypeScript types
4. Modify API endpoints as needed

## 🐛 Debugging Tips

### Frontend Issues
- Check browser console for errors
- Use React DevTools for component inspection
- Verify API calls in Network tab
- Check environment variables in `.env`

### Backend Issues
- Check FastAPI logs in terminal
- Use `/docs` endpoint for API testing
- Verify database connections
- Check environment variables

### Common Problems
- **CORS errors**: Check CORS settings in `backend/main.py`
- **Auth issues**: Verify JWT token and SECRET_KEY
- **File upload fails**: Check Cloudinary/storage configuration
- **AI features not working**: Verify API keys for OpenAI/ElevenLabs

## 📦 Dependencies Overview

### Frontend Key Packages
- `react` & `react-dom` - Core React
- `typescript` - Type safety
- `vite` - Build tool
- `tailwindcss` - Styling
- `@radix-ui/*` - UI primitives
- `react-router-dom` - Routing
- `@supabase/supabase-js` - Backend integration

### Backend Key Packages
- `fastapi` - Web framework
- `uvicorn` - ASGI server
- `sqlalchemy` - ORM
- `moviepy` - Video processing
- `openai` - AI integration
- `cloudinary` - Media storage

## 🚀 Deployment Checklist

### Frontend (Vercel)
- [ ] Environment variables configured
- [ ] Build command: `npm run build`
- [ ] Output directory: `dist`
- [ ] Domain configured

### Backend (Render/Railway)
- [ ] Environment variables set
- [ ] Requirements.txt updated
- [ ] Database URL configured
- [ ] Health check endpoint working

### Database
- [ ] Production database setup
- [ ] Migrations applied
- [ ] Backup strategy in place
- [ ] Connection pooling configured

This quick reference provides immediate access to the most commonly needed information for SmartClips development.
