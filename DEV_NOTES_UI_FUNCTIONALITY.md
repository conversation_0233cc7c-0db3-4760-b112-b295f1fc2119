# UI Functionality & Editing Tools Fix

## 1. Video Editor Component Integration

**File**: `src/components/VideoEditor.tsx`
**Status**: Needs to be connected to backend API

```typescript
// Enhanced Video Editor with backend integration
import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { useToast } from '@/hooks/use-toast';
import apiService from '@/services/api';

interface VideoEditorProps {
  videoFile: File | null;
  videoUrl?: string;
  onVideoProcessed?: (processedVideo: any) => void;
}

const VideoEditor: React.FC<VideoEditorProps> = ({ 
  videoFile, 
  videoUrl, 
  onVideoProcessed 
}) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [trimStart, setTrimStart] = useState(0);
  const [trimEnd, setTrimEnd] = useState(100);
  const [speed, setSpeed] = useState(1);
  const [cropCoords, setCropCoords] = useState({ x1: 0, y1: 0, x2: 100, y2: 100 });
  const { toast } = useToast();

  const handleTrim = async () => {
    if (!videoFile) return;
    
    setIsProcessing(true);
    try {
      const result = await apiService.trimVideo(videoFile, trimStart, trimEnd);
      onVideoProcessed?.(result);
      toast({
        title: "Video trimmed successfully",
        description: "Your video has been trimmed",
      });
    } catch (error: any) {
      toast({
        title: "Trim failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleSpeedAdjust = async () => {
    if (!videoFile) return;
    
    setIsProcessing(true);
    try {
      const result = await apiService.adjustSpeed(videoFile, speed);
      onVideoProcessed?.(result);
      toast({
        title: "Speed adjusted successfully",
        description: `Video speed changed to ${speed}x`,
      });
    } catch (error: any) {
      toast({
        title: "Speed adjustment failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCrop = async () => {
    if (!videoFile) return;
    
    setIsProcessing(true);
    try {
      const result = await apiService.cropVideo(
        videoFile, 
        cropCoords.x1, 
        cropCoords.y1, 
        cropCoords.x2, 
        cropCoords.y2
      );
      onVideoProcessed?.(result);
      toast({
        title: "Video cropped successfully",
        description: "Your video has been cropped",
      });
    } catch (error: any) {
      toast({
        title: "Crop failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Video Preview */}
      <Card>
        <CardHeader>
          <CardTitle>Video Preview</CardTitle>
        </CardHeader>
        <CardContent>
          {videoUrl && (
            <video 
              src={videoUrl} 
              controls 
              className="w-full max-w-md mx-auto rounded-lg"
            />
          )}
        </CardContent>
      </Card>

      {/* Trim Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Trim Video</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium">Start Time (%)</label>
            <Slider
              value={[trimStart]}
              onValueChange={(value) => setTrimStart(value[0])}
              max={100}
              step={1}
              className="mt-2"
            />
          </div>
          <div>
            <label className="text-sm font-medium">End Time (%)</label>
            <Slider
              value={[trimEnd]}
              onValueChange={(value) => setTrimEnd(value[0])}
              max={100}
              step={1}
              className="mt-2"
            />
          </div>
          <Button onClick={handleTrim} disabled={isProcessing || !videoFile}>
            {isProcessing ? 'Trimming...' : 'Trim Video'}
          </Button>
        </CardContent>
      </Card>

      {/* Speed Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Adjust Speed</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium">Speed: {speed}x</label>
            <Slider
              value={[speed]}
              onValueChange={(value) => setSpeed(value[0])}
              min={0.25}
              max={4}
              step={0.25}
              className="mt-2"
            />
          </div>
          <Button onClick={handleSpeedAdjust} disabled={isProcessing || !videoFile}>
            {isProcessing ? 'Adjusting...' : 'Adjust Speed'}
          </Button>
        </CardContent>
      </Card>

      {/* Crop Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Crop Video</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">X1 (%)</label>
              <Slider
                value={[cropCoords.x1]}
                onValueChange={(value) => setCropCoords({...cropCoords, x1: value[0]})}
                max={100}
                step={1}
                className="mt-2"
              />
            </div>
            <div>
              <label className="text-sm font-medium">Y1 (%)</label>
              <Slider
                value={[cropCoords.y1]}
                onValueChange={(value) => setCropCoords({...cropCoords, y1: value[0]})}
                max={100}
                step={1}
                className="mt-2"
              />
            </div>
            <div>
              <label className="text-sm font-medium">X2 (%)</label>
              <Slider
                value={[cropCoords.x2]}
                onValueChange={(value) => setCropCoords({...cropCoords, x2: value[0]})}
                max={100}
                step={1}
                className="mt-2"
              />
            </div>
            <div>
              <label className="text-sm font-medium">Y2 (%)</label>
              <Slider
                value={[cropCoords.y2]}
                onValueChange={(value) => setCropCoords({...cropCoords, y2: value[0]})}
                max={100}
                step={1}
                className="mt-2"
              />
            </div>
          </div>
          <Button onClick={handleCrop} disabled={isProcessing || !videoFile}>
            {isProcessing ? 'Cropping...' : 'Crop Video'}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default VideoEditor;
```

## 2. Analytics Dashboard Fix

**File**: `src/pages/Analytics.tsx`
**Current Issue**: Not connected to backend data

```typescript
// Enhanced Analytics with backend integration
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/context/AuthContext';
import apiService from '@/services/api';

const Analytics = () => {
  const { user } = useAuth();
  const [videos, setVideos] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchVideos = async () => {
      try {
        const result = await apiService.getVideos();
        setVideos(result.videos || []);
      } catch (error) {
        console.error('Failed to fetch videos:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (user) {
      fetchVideos();
    }
  }, [user]);

  // Rest of component implementation...
};
```

## 3. Login/Logout Button Fixes

**Files to Update**:
- `src/components/Navbar.tsx` (lines 20-45)
- `src/pages/Login.tsx` (lines 45-80)
- `src/pages/Register.tsx` (lines 40-75)

**Key Issues Fixed**:
1. OAuth buttons now properly handle errors
2. Login/logout state management improved
3. Navigation after authentication fixed

## 4. SmartClipper Integration

**File**: `src/pages/SmartClipper.tsx`
**Current Issue**: Not connected to video processing backend

```typescript
// Add to SmartClipper component
const handleVideoUpload = async (file: File) => {
  setIsProcessing(true);
  try {
    const result = await apiService.uploadVideo(file, minDuration, maxDuration);
    setProcessedVideo(result);
    // Show video editor
    setShowEditor(true);
  } catch (error: any) {
    toast({
      title: "Upload failed",
      description: error.message,
      variant: "destructive",
    });
  } finally {
    setIsProcessing(false);
  }
};

const handleUrlProcess = async (url: string) => {
  setIsProcessing(true);
  try {
    // First validate URL
    await apiService.validateUrl(url);
    // Then process
    const result = await apiService.processUrl(url, minDuration, maxDuration);
    setProcessedVideo(result);
    setShowEditor(true);
  } catch (error: any) {
    toast({
      title: "URL processing failed",
      description: error.message,
      variant: "destructive",
    });
  } finally {
    setIsProcessing(false);
  }
};
```

## 5. Testing Checklist

**Frontend Tests**:
```bash
# 1. Test login/logout buttons
# Visit: http://localhost:8080/login
# Click login/logout buttons

# 2. Test video upload
# Visit: http://localhost:8080/smart-clipper
# Upload a video file

# 3. Test analytics
# Visit: http://localhost:8080/analytics
# Check if data loads

# 4. Test editing tools
# Upload video → should show editor
# Try trim, speed, crop functions
```

**Backend Tests**:
```bash
# Test video processing endpoints
curl -X POST http://localhost:8000/upload \
  -F "file=@test_video.mp4" \
  -F "min_duration=10" \
  -F "max_duration=60"

# Test editing endpoints
curl -X POST http://localhost:8000/edit/trim \
  -F "file=@test_video.mp4" \
  -F "start_time=5" \
  -F "end_time=15"
```

## 6. Component Integration Map

```
SmartClipper → VideoEditor → Backend API
     ↓              ↓            ↓
  Upload UI    Editing Tools   Processing
     ↓              ↓            ↓
  Analytics ← Video Storage ← Database
```

**Files that need updates**:
1. `src/pages/SmartClipper.tsx` - Connect to backend
2. `src/components/VideoEditor.tsx` - Add editing functionality
3. `src/pages/Analytics.tsx` - Connect to video data
4. `src/components/Navbar.tsx` - Fix login/logout buttons
5. `src/services/api.ts` - Create API service layer
