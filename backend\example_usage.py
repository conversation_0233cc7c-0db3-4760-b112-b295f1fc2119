#!/usr/bin/env python3
"""
Example usage of the Advanced Video Processor
This script demonstrates how to use the various features of the advanced video processing system
"""

import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from advanced_video_processor import (
    AdvancedVideoProcessor, 
    process_video_with_enhancements, 
    create_quick_tiktok_clip
)

def example_1_basic_processing():
    """
    Example 1: Basic video processing with all features enabled
    """
    print("🎬 Example 1: Basic Processing")
    print("-" * 40)
    
    # Input video path (replace with your video)
    input_video = "sample_video.mp4"
    output_directory = "./processed_output"
    
    # Create output directory
    os.makedirs(output_directory, exist_ok=True)
    
    # Process with default options
    try:
        results = process_video_with_enhancements(
            video_path=input_video,
            output_dir=output_directory,
            openai_api_key=os.getenv("OPENAI_API_KEY")
        )
        
        if results.get('error'):
            print(f"❌ Processing failed: {results['error']}")
        else:
            print(f"✅ Processing completed in {results['processing_time']:.2f} seconds")
            print(f"📁 Output files: {list(results['processed_videos'].keys())}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def example_2_custom_options():
    """
    Example 2: Custom processing options for specific platforms
    """
    print("\n🎨 Example 2: Custom Options")
    print("-" * 40)
    
    input_video = "sample_video.mp4"
    output_directory = "./custom_output"
    
    # Create output directory
    os.makedirs(output_directory, exist_ok=True)
    
    # Custom options for TikTok-style content
    custom_options = {
        'add_subtitles': True,
        'add_emojis': True,
        'add_clipart': False,  # Disable clipart for cleaner look
        'create_short_form': True,
        'platforms': ['tiktok'],  # Only TikTok format
        'subtitle_style': 'tiktok',  # TikTok-style subtitles
        'max_short_clips': 5  # Create up to 5 clips
    }
    
    try:
        processor = AdvancedVideoProcessor(openai_api_key=os.getenv("OPENAI_API_KEY"))
        results = processor.process_video_comprehensive(
            video_path=input_video,
            output_dir=output_directory,
            options=custom_options
        )
        
        if results.get('error'):
            print(f"❌ Processing failed: {results['error']}")
        else:
            print(f"✅ Custom processing completed!")
            print(f"📱 TikTok clips created: {len(results['short_form_clips'].get('tiktok', []))}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def example_3_step_by_step():
    """
    Example 3: Step-by-step processing for fine control
    """
    print("\n🔧 Example 3: Step-by-Step Processing")
    print("-" * 40)
    
    input_video = "sample_video.mp4"
    output_directory = "./step_by_step_output"
    
    # Create output directory
    os.makedirs(output_directory, exist_ok=True)
    
    try:
        # Initialize processor
        processor = AdvancedVideoProcessor(openai_api_key=os.getenv("OPENAI_API_KEY"))
        
        # Step 1: Extract transcript
        print("🎤 Step 1: Extracting transcript...")
        transcript_data = processor.extract_transcript_with_timing(input_video)
        print(f"   Extracted {len(transcript_data)} words")
        
        # Step 2: Add emotions and emojis
        print("😊 Step 2: Analyzing emotions...")
        enhanced_transcript = processor.analyze_emotions_and_add_emojis(transcript_data)
        emoji_count = sum(1 for word in enhanced_transcript if word.get('emoji'))
        print(f"   Added {emoji_count} emojis")
        
        # Step 3: Extract key nouns
        print("🔍 Step 3: Extracting key nouns...")
        key_nouns = processor.extract_key_nouns(transcript_data)
        print(f"   Found {len(key_nouns)} key nouns")
        
        # Step 4: Create subtitled video
        print("📝 Step 4: Creating subtitled video...")
        subtitle_output = os.path.join(output_directory, "subtitled_video.mp4")
        processor.create_animated_subtitles(
            input_video, 
            enhanced_transcript, 
            subtitle_output, 
            style="modern"
        )
        print(f"   Saved: {subtitle_output}")
        
        # Step 5: Create short-form clips
        print("📱 Step 5: Creating short-form clips...")
        short_clips = processor.create_short_form_content(
            subtitle_output, 
            platform='instagram', 
            max_clips=3
        )
        print(f"   Created {len(short_clips)} Instagram clips")
        
        print("✅ Step-by-step processing completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")

def example_4_quick_tiktok():
    """
    Example 4: Quick TikTok clip creation
    """
    print("\n⚡ Example 4: Quick TikTok Clip")
    print("-" * 40)
    
    input_video = "sample_video.mp4"
    output_path = "./quick_tiktok_clip.mp4"
    
    try:
        result_path = create_quick_tiktok_clip(
            video_path=input_video,
            output_path=output_path,
            openai_api_key=os.getenv("OPENAI_API_KEY")
        )
        
        print(f"✅ Quick TikTok clip created: {result_path}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

def example_5_batch_processing():
    """
    Example 5: Batch processing multiple videos
    """
    print("\n📦 Example 5: Batch Processing")
    print("-" * 40)
    
    # List of input videos
    input_videos = [
        "video1.mp4",
        "video2.mp4",
        "video3.mp4"
    ]
    
    output_base_dir = "./batch_output"
    
    # Process each video
    for i, video_path in enumerate(input_videos):
        if not os.path.exists(video_path):
            print(f"⚠️ Skipping {video_path} (file not found)")
            continue
            
        print(f"🎬 Processing video {i+1}/{len(input_videos)}: {video_path}")
        
        # Create individual output directory
        video_output_dir = os.path.join(output_base_dir, f"video_{i+1}")
        os.makedirs(video_output_dir, exist_ok=True)
        
        try:
            # Quick processing for batch
            options = {
                'add_subtitles': True,
                'add_emojis': True,
                'add_clipart': False,  # Skip clipart for faster processing
                'create_short_form': True,
                'platforms': ['tiktok'],
                'subtitle_style': 'modern',
                'max_short_clips': 2
            }
            
            results = process_video_with_enhancements(
                video_path=video_path,
                output_dir=video_output_dir,
                options=options
            )
            
            if results.get('error'):
                print(f"   ❌ Failed: {results['error']}")
            else:
                print(f"   ✅ Completed in {results['processing_time']:.2f}s")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print("📦 Batch processing completed!")

def main():
    """
    Main function to run all examples
    """
    print("🎬 Advanced Video Processor Examples")
    print("=" * 60)
    
    # Check if sample video exists
    sample_video = "sample_video.mp4"
    if not os.path.exists(sample_video):
        print(f"⚠️ Sample video '{sample_video}' not found.")
        print("Please provide a sample video file to run these examples.")
        print("You can download a sample video or use your own video file.")
        return
    
    # Check for OpenAI API key
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠️ OPENAI_API_KEY environment variable not set.")
        print("Some features may not work without the API key.")
    
    # Run examples
    try:
        example_1_basic_processing()
        example_2_custom_options()
        example_3_step_by_step()
        example_4_quick_tiktok()
        example_5_batch_processing()
        
        print("\n🎉 All examples completed!")
        print("Check the output directories for processed videos.")
        
    except KeyboardInterrupt:
        print("\n⏹️ Examples interrupted by user.")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
