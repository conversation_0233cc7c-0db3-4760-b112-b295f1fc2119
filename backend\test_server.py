#!/usr/bin/env python3
"""
SmartClips Test Backend Server
Simplified version for testing frontend functionality without video processing dependencies
"""

from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, List
import uvicorn
import time
import random

# Initialize FastAPI app
app = FastAPI(title="SmartClips Test API", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:8080", "http://localhost:3000", "http://127.0.0.1:8080"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class URLRequest(BaseModel):
    url: str

class URLValidationResponse(BaseModel):
    valid: bool
    platform: Optional[str] = None
    video_id: Optional[str] = None
    error: Optional[str] = None

class VideoMetadata(BaseModel):
    title: str
    duration: int
    uploader: str
    view_count: int
    platform: str
    thumbnail: str
    description: str
    upload_date: str

class URLProcessRequest(BaseModel):
    url: str
    min_duration: float = 10.0
    max_duration: float = 60.0
    quality: str = "best"

class VideoSegment(BaseModel):
    start_time: float
    end_time: float
    text: str

class ProcessedURLResult(BaseModel):
    segments: List[VideoSegment]
    video_urls: List[str]

# Helper functions
def detect_platform(url: str) -> Optional[str]:
    """Detect video platform from URL"""
    if "youtube.com" in url or "youtu.be" in url:
        return "youtube"
    elif "tiktok.com" in url:
        return "tiktok"
    elif "instagram.com" in url:
        return "instagram"
    elif "twitter.com" in url or "x.com" in url:
        return "twitter"
    elif "facebook.com" in url:
        return "facebook"
    return None

def extract_video_id(url: str) -> Optional[str]:
    """Extract video ID from URL"""
    if "youtube.com" in url:
        if "v=" in url:
            return url.split("v=")[1].split("&")[0]
    elif "youtu.be" in url:
        return url.split("/")[-1].split("?")[0]
    elif "tiktok.com" in url:
        if "/video/" in url:
            return url.split("/video/")[1].split("?")[0]
    return "mock_video_id"

# API Endpoints
@app.get("/")
async def root():
    return {"message": "SmartClips Test API is running!", "status": "healthy"}

@app.post("/validate-url", response_model=URLValidationResponse)
async def validate_video_url(request: URLRequest):
    """Validate if URL is from a supported platform - Public endpoint"""
    try:
        platform = detect_platform(request.url)
        video_id = extract_video_id(request.url)
        
        if platform:
            return URLValidationResponse(
                valid=True,
                platform=platform,
                video_id=video_id
            )
        else:
            return URLValidationResponse(
                valid=False,
                error="Unsupported platform. Please use YouTube, TikTok, Instagram, Twitter, or Facebook URLs."
            )
    except Exception as e:
        return URLValidationResponse(
            valid=False,
            error=f"URL validation error: {str(e)}"
        )

@app.post("/url-metadata", response_model=VideoMetadata)
async def get_url_metadata(request: URLRequest):
    """Get video metadata from URL - Public endpoint"""
    try:
        platform = detect_platform(request.url)
        video_id = extract_video_id(request.url)
        
        # Generate mock metadata based on platform
        platform_titles = {
            "youtube": "Amazing YouTube Video - Tutorial & Tips",
            "tiktok": "Viral TikTok Dance Challenge",
            "instagram": "Instagram Reel - Behind the Scenes",
            "twitter": "Twitter Video - Breaking News",
            "facebook": "Facebook Video - Family Moments"
        }
        
        platform_creators = {
            "youtube": "TechGuru2024",
            "tiktok": "DanceQueen",
            "instagram": "LifestyleBlogger",
            "twitter": "NewsReporter",
            "facebook": "FamilyChannel"
        }
        
        return VideoMetadata(
            title=platform_titles.get(platform, "Video from Unknown Platform"),
            duration=random.randint(60, 300),  # 1-5 minutes
            uploader=platform_creators.get(platform, "Unknown Creator"),
            view_count=random.randint(1000, 1000000),
            platform=platform or "unknown",
            thumbnail=f"https://picsum.photos/320/180?random={hash(video_id) % 1000}",
            description=f"This is a sample {platform} video for testing SmartClips functionality. The actual video processing would extract real metadata.",
            upload_date="2024-01-15"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Metadata extraction error: {str(e)}")

@app.post("/process-url", response_model=ProcessedURLResult)
async def process_video_url(request: URLProcessRequest):
    """Download and process video from URL - Public endpoint"""
    try:
        platform = detect_platform(request.url)
        
        # Simulate processing time
        time.sleep(2)
        
        # Generate mock segments based on duration constraints
        num_segments = random.randint(2, 5)
        segments = []
        
        segment_texts = [
            "Engaging opening hook that captures attention",
            "Key insight or main point of the video",
            "Emotional moment that resonates with viewers",
            "Call to action or important conclusion",
            "Viral moment with high engagement potential"
        ]
        
        for i in range(num_segments):
            start_time = i * 30
            end_time = start_time + random.randint(int(request.min_duration), int(request.max_duration))
            
            segments.append(VideoSegment(
                start_time=start_time,
                end_time=end_time,
                text=segment_texts[i % len(segment_texts)]
            ))
        
        # Generate mock video URLs (using placeholder videos)
        video_urls = [
            f"https://sample-videos.com/zip/10/mp4/SampleVideo_{i+1}mb.mp4"
            for i in range(num_segments)
        ]
        
        return ProcessedURLResult(
            segments=segments,
            video_urls=video_urls
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Video processing error: {str(e)}")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "message": "SmartClips Test Backend is running"
    }

# Error handlers
@app.exception_handler(404)
async def not_found_handler(request, exc):
    return {"error": "Endpoint not found", "status_code": 404}

@app.exception_handler(500)
async def internal_error_handler(request, exc):
    return {"error": "Internal server error", "status_code": 500}

if __name__ == "__main__":
    print("🚀 Starting SmartClips Test Backend Server...")
    print("📍 Server will be available at: http://localhost:8000")
    print("📋 API Documentation: http://localhost:8000/docs")
    print("🔧 This is a test server for frontend development")
    print("⚡ Ready to process URL validation and mock video processing!")
    
    uvicorn.run(
        "test_server:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )
